{% extends 'base.html' %}
{% load static %}

{% block title %}Order History - Fashion Store{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">Order History</h2>
            
            {% if page_obj %}
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Date</th>
                                    <th>Items</th>
                                    <th>Status</th>
                                    <th>Total</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in page_obj %}
                                <tr>
                                    <td>
                                        <strong>{{ order.order_number }}</strong>
                                    </td>
                                    <td>{{ order.created_at|date:"M d, Y" }}</td>
                                    <td>{{ order.items.count }} item{{ order.items.count|pluralize }}</td>
                                    <td>
                                        <span class="badge bg-{% if order.status == 'delivered' %}success{% elif order.status == 'shipped' %}info{% elif order.status == 'processing' %}warning{% elif order.status == 'cancelled' %}danger{% else %}secondary{% endif %}">
                                            {{ order.get_status_display }}
                                        </span>
                                    </td>
                                    <td><strong>${{ order.total_amount }}</strong></td>
                                    <td>
                                        <a href="{{ order.get_absolute_url }}" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye me-1"></i>View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Order pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
            {% else %}
            <!-- No Orders -->
            <div class="text-center py-5">
                <i class="fas fa-shopping-bag fa-5x text-muted mb-4"></i>
                <h3>No Orders Yet</h3>
                <p class="text-muted mb-4">You haven't placed any orders yet. Start shopping to see your order history here.</p>
                <a href="{% url 'store:product_list' %}" class="btn btn-primary btn-lg">
                    <i class="fas fa-shopping-cart me-2"></i>Start Shopping
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
