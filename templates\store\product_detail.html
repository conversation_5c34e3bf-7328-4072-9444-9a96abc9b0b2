{% extends 'base.html' %}
{% load static %}

{% block title %}{{ product.name }} - Fashion Store{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'store:home' %}">Home</a></li>
            <li class="breadcrumb-item"><a href="{% url 'store:product_list' %}">Products</a></li>
            <li class="breadcrumb-item"><a href="{{ product.category.get_absolute_url }}">{{ product.category.name }}</a></li>
            <li class="breadcrumb-item active">{{ product.name }}</li>
        </ol>
    </nav>
    
    <div class="row">
        <!-- Product Image -->
        <div class="col-lg-6 mb-4">
            <div class="product-image-container">
                {% if product.image %}
                <img src="{{ product.image.url }}" class="img-fluid rounded shadow" alt="{{ product.name }}" style="width: 100%; height: 500px; object-fit: cover;">
                {% else %}
                <div class="bg-light d-flex align-items-center justify-content-center rounded shadow" style="height: 500px;">
                    <i class="fas fa-image text-muted fa-5x"></i>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Product Details -->
        <div class="col-lg-6">
            <div class="product-details">
                <h1 class="display-5 fw-bold mb-3">{{ product.name }}</h1>
                
                <!-- Price -->
                <div class="product-price mb-4">
                    {% if product.discount_price %}
                    <span class="h3 text-primary">${{ product.get_price }}</span>
                    <span class="h5 text-muted text-decoration-line-through ms-2">${{ product.price }}</span>
                    <span class="badge bg-danger ms-2">{{ product.get_discount_percentage }}% OFF</span>
                    {% else %}
                    <span class="h3 text-primary">${{ product.get_price }}</span>
                    {% endif %}
                </div>
                
                <!-- Description -->
                <div class="product-description mb-4">
                    <h5>Description</h5>
                    <p class="text-muted">{{ product.description }}</p>
                </div>
                
                <!-- Stock Status -->
                <div class="stock-status mb-4">
                    {% if product.stock > 0 %}
                    <span class="badge bg-success">In Stock ({{ product.stock }} available)</span>
                    {% else %}
                    <span class="badge bg-danger">Out of Stock</span>
                    {% endif %}
                </div>
                
                <!-- Size Selection -->
                {% if product.get_available_sizes_list %}
                <div class="size-selection mb-4">
                    <h6>Size:</h6>
                    <div class="btn-group" role="group" aria-label="Size selection">
                        {% for size in product.get_available_sizes_list %}
                        <input type="radio" class="btn-check" name="size" id="size-{{ size }}" value="{{ size }}">
                        <label class="btn btn-outline-primary" for="size-{{ size }}">{{ size }}</label>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                
                <!-- Quantity and Add to Cart -->
                {% if product.stock > 0 %}
                <div class="add-to-cart-section mb-4">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <label for="quantity" class="form-label">Quantity:</label>
                            <input type="number" class="form-control" id="quantity" value="1" min="1" max="{{ product.stock }}">
                        </div>
                        <div class="col-md-8">
                            <button class="btn btn-primary btn-lg w-100 add-to-cart-btn" data-product-id="{{ product.id }}">
                                <i class="fas fa-cart-plus me-2"></i>Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- Product Info -->
                <div class="product-info">
                    <ul class="list-unstyled">
                        <li class="mb-2"><strong>Category:</strong> <a href="{{ product.category.get_absolute_url }}" class="text-decoration-none">{{ product.category.name }}</a></li>
                        <li class="mb-2"><strong>SKU:</strong> {{ product.id|stringformat:"05d" }}</li>
                        {% if product.get_available_sizes_list %}
                        <li class="mb-2"><strong>Available Sizes:</strong> {{ product.available_sizes }}</li>
                        {% endif %}
                    </ul>
                </div>
                
                <!-- Share Buttons -->
                <div class="share-buttons mt-4">
                    <h6>Share:</h6>
                    <a href="#" class="btn btn-outline-primary btn-sm me-2"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" class="btn btn-outline-info btn-sm me-2"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="btn btn-outline-success btn-sm me-2"><i class="fab fa-whatsapp"></i></a>
                    <a href="#" class="btn btn-outline-danger btn-sm"><i class="fab fa-pinterest"></i></a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Related Products -->
    {% if related_products %}
    <section class="related-products mt-5">
        <h3 class="mb-4">Related Products</h3>
        <div class="row">
            {% for related_product in related_products %}
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card product-card h-100">
                    {% if related_product.get_discount_percentage %}
                    <span class="badge bg-danger">{{ related_product.get_discount_percentage }}% OFF</span>
                    {% endif %}
                    {% if related_product.image %}
                    <img src="{{ related_product.image.url }}" class="card-img-top" alt="{{ related_product.name }}">
                    {% else %}
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                        <i class="fas fa-image text-muted fa-2x"></i>
                    </div>
                    {% endif %}
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title">{{ related_product.name }}</h6>
                        <div class="product-price mb-3">
                            {% if related_product.discount_price %}
                            <span class="original-price">${{ related_product.price }}</span>
                            {% endif %}
                            ${{ related_product.get_price }}
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-auto">
                            <a href="{{ related_product.get_absolute_url }}" class="btn btn-outline-primary btn-sm">View</a>
                            <button class="btn btn-primary btn-sm add-to-cart" data-product-id="{{ related_product.id }}">
                                <i class="fas fa-cart-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </section>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const addToCartBtn = document.querySelector('.add-to-cart-btn');
        const quantityInput = document.querySelector('#quantity');
        const sizeInputs = document.querySelectorAll('input[name="size"]');
        
        if (addToCartBtn) {
            addToCartBtn.addEventListener('click', function() {
                const productId = this.dataset.productId;
                const quantity = quantityInput ? quantityInput.value : 1;
                let selectedSize = '';
                
                // Get selected size
                sizeInputs.forEach(input => {
                    if (input.checked) {
                        selectedSize = input.value;
                    }
                });
                
                // Check if size is required but not selected
                if (sizeInputs.length > 0 && !selectedSize) {
                    alert('Please select a size');
                    return;
                }
                
                // Add to cart
                addToCart(productId, selectedSize, quantity);
            });
        }
    });
    
    function addToCart(productId, size = '', quantity = 1) {
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        
        fetch('{% url "store:cart_add" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': csrfToken
            },
            body: `product_id=${productId}&size=${size}&quantity=${quantity}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Product added to cart!', 'success');
                updateCartCount(data.cart_count);
            } else {
                showNotification('Error adding product to cart', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error adding product to cart', 'error');
        });
    }
</script>
{% endblock %}
