{% extends 'base.html' %}
{% load static %}

{% block title %}Register - Fashion Store{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <h2 class="fw-bold">Create Account</h2>
                        <p class="text-muted">Join our fashion community</p>
                    </div>
                    
                    {% if form.errors %}
                    <div class="alert alert-danger">
                        {% for field, errors in form.errors.items %}
                            {% for error in errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">Username</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                {{ form.username }}
                            </div>
                            {% if form.username.help_text %}
                            <small class="form-text text-muted">{{ form.username.help_text }}</small>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.password1.id_for_label }}" class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                {{ form.password1 }}
                            </div>
                            {% if form.password1.help_text %}
                            <small class="form-text text-muted">{{ form.password1.help_text }}</small>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.password2.id_for_label }}" class="form-label">Confirm Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                {{ form.password2 }}
                            </div>
                            {% if form.password2.help_text %}
                            <small class="form-text text-muted">{{ form.password2.help_text }}</small>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="#" class="text-decoration-none">Terms of Service</a> and 
                                <a href="#" class="text-decoration-none">Privacy Policy</a>
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-user-plus me-2"></i>Create Account
                        </button>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-0">Already have an account? 
                            <a href="{% url 'login' %}" class="text-decoration-none fw-bold">Sign in here</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: none;
        border-radius: 15px;
    }
    
    .form-control {
        border-left: none;
        border-radius: 0 10px 10px 0;
    }
    
    .input-group-text {
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: 10px 0 0 10px;
    }
    
    .form-control:focus {
        box-shadow: none;
        border-color: var(--primary-color);
    }
    
    .form-text {
        font-size: 0.8rem;
    }
</style>
{% endblock %}
