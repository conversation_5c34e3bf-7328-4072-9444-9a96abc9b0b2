# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2021
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2022
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2023-04-24 20:19+0000\n"
"Last-Translator: Swara <<EMAIL>>, 2022\n"
"Language-Team: Central Kurdish (http://www.transifex.com/django/django/"
"language/ckb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ckb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "بەڵگەنامەکردنی کارگێڕی"

msgid "Home"
msgstr "ماڵەوە"

msgid "Documentation"
msgstr "بەڵگەنامەکردن"

msgid "Bookmarklets"
msgstr "ئاماژەکان"

msgid "Documentation bookmarklets"
msgstr "بەڵگەنامەکردنی ئاماژەکان"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"بۆ دامەزراندنی ئاماژەکان، بەستەرەکە ڕابکێشە بۆ سەر جێدۆزەکانت، یان کرتەی "
"ڕاست لەسەر بەستەرەکە بکە و زیادی بکە بۆ ئاماژەکانت. ئێستا دەتوانیت لە هەر "
"لاپەڕەیەک لە ماڵپەڕەکەدا ئاماژە هەڵبژێریت."

msgid "Documentation for this page"
msgstr "بەڵگەنامەکردن بۆ ئەم لاپەڕە"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"لە هەر لاپەڕەیەکەوە دەچێتەسەر بەڵگەنامەکان بۆ ئەو دیمەنەی کە ئەو لاپەڕەیە "
"دروست دەکات."

msgid "Tags"
msgstr "تاگەکان"

msgid "List of all the template tags and their functions."
msgstr "لیستی هەموو تاگەکانی ڕووکار و ئەرکەکانیان."

msgid "Filters"
msgstr "پاڵاوتنەکان"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"پاڵاوتنەکان ئەو کردارانەن کە دەتوانرێت بۆ گۆڕاوەکان لە ڕووکارێکدا "
"بەکاربهێنرێن بۆ گۆڕینی دەرچوێنراوەکە."

msgid "Models"
msgstr "مۆدیلەکان"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"مۆدێلەکان بریتین لە باسکردنی هەموو شتەکانی ناو سیستەمەکە و بوارە "
"پەیوەندیدارەکانیان. هەر مۆدێلێک لیستی خانەی هەیە کە دەتوانرێت وەک گۆڕاوەکانی "
"ڕووکار دەستی پێ بگات"

msgid "Views"
msgstr "دیمەنەکان"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"هەر لاپەڕەیەک لە پێگەی گشتیدا بە دیمەنێک دروست دەکرێت. دیمەنەکە ئەوە پێناسە "
"دەکات کە کام ڕووکار بۆ دروستکردنی لاپەڕەکە بەکاردەهێنرێت و کام ئۆبجێکت "
"بەردەستە بۆ ئەو ڕووکارە."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"ئامرازەکان بۆ وێبگەڕەکەت بۆ ئەوەی بە خێرایی دەستت بگات بە کاراییەکانی "
"بەڕێوەبەر."

msgid "Please install docutils"
msgstr "تکایە docutils دابمەزرێنە"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a "
"href=\"%(link)s\">docutils</a> library."
msgstr ""
"سیستەمی بەڵگەنامەکردنی بەڕێوەبەر پێویستی بە کتێبخانەی <a "
"href=\"%(link)s\">docutils</a> پایسۆن هەیە."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"تکایە پرسیار لە بەڕێوەبەرایەتی بکە بۆ دامەزراندنی <a "
"href=\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "مۆدێل: %(name)s"

msgid "Fields"
msgstr "خانەکان"

msgid "Field"
msgstr "خانە"

msgid "Type"
msgstr "جۆر"

msgid "Description"
msgstr "باسکردن"

msgid "Methods with arguments"
msgstr "شێوازەکان و ئارگومێنتەکان"

msgid "Method"
msgstr "شێواز"

msgid "Arguments"
msgstr "ئارگومێنتەکان"

msgid "Back to Model documentation"
msgstr "گەڕانەوە بۆ بەڵگەنامەکردنی مۆدێل"

msgid "Model documentation"
msgstr "بەڵگەنامەی مۆدێل"

msgid "Model groups"
msgstr "گرووپەکانی مۆدێل"

msgid "Templates"
msgstr "ڕووکارەکان"

#, python-format
msgid "Template: %(name)s"
msgstr "ڕووکار: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "ڕووکاری: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "گەڕانی ڕێڕەو بۆ ڕووکاری <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(بوونی نیە)"

msgid "Back to Documentation"
msgstr "گەڕانەوە بۆ بەڵگەنامەکردن"

msgid "Template filters"
msgstr "پاڵاوتنەکانی ڕووکار"

msgid "Template filter documentation"
msgstr "بەڵگەنامەکردنی پاڵاوتنی ڕووکار"

msgid "Built-in filters"
msgstr "پاڵاوتنەکانی Built-in"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"بۆ بەکارهێنانی ئەم پاڵاوتنانە<code>%(code)s</code> لەناو ڕووکارەکەت دابنێ "
"پێش ئەوەی پاڵاوتن بەکاربهێنیت."

msgid "Template tags"
msgstr "تاگەکانی ڕووکار"

msgid "Template tag documentation"
msgstr "بەڵگەنامەکردنی تاگی ڕووکار"

msgid "Built-in tags"
msgstr "تاگەکانی Built-in"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"بۆ بەکارهێنانی ئەم تاگانە, <code>%(code)s</code> لەناو ڕووکارەکەت دابنێ پێش "
"ئەوەی تاگ بەکاربهێنیت."

#, python-format
msgid "View: %(name)s"
msgstr "دیمەن: %(name)s"

msgid "Context:"
msgstr "دەق:"

msgid "Templates:"
msgstr "ڕووکارەکان:"

msgid "Back to View documentation"
msgstr "گەڕانەوە بۆ بەڵگەنامەکردنی دیمەن"

msgid "View documentation"
msgstr "بەڵگەنامەکردنی دیمەن"

msgid "Jump to namespace"
msgstr "بازدان بۆ بۆشاییناو"

msgid "Empty namespace"
msgstr "بۆشاییناوی بەتاڵ"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "دیمەنەکان لەلایەن بۆشاییناو %(name)s"

msgid "Views by empty namespace"
msgstr "دیمەنەکان لایەن بۆشاییناوی بەتاڵەوە"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    کرداری دیمەن: <code>%(full_name)s</code>. ناو: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "تاگ:"

msgid "filter:"
msgstr "پاڵاوتن:"

msgid "view:"
msgstr "دیمەن:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "بەرنامەی %(app_label)r نەدۆزرایەوە"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "مۆدێلی %(model_name)r ناوتوانرێت لە ئاپی %(app_label)r بدۆزرێتەوە"

msgid "model:"
msgstr "مۆدێل:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "پەیوەستکراو `%(app_label)s. ئۆبجێکتی%(data_type)s`"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "پەیوەستکراوە `%(app_label)s. ئۆبجێکتی%(object_name)s`"

#, python-format
msgid "all %s"
msgstr "هەموو %s"

#, python-format
msgid "number of %s"
msgstr "ژمارە لە %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s بەگوێرەی شێوازی urlی ئۆبجێکتێک نیە"
