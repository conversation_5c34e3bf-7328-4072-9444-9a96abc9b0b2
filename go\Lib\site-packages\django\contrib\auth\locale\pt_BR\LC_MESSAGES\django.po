# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON>iss<PERSON> A<PERSON> <<EMAIL>>, 2014
# <PERSON> <amanda<PERSON><PERSON><PERSON><PERSON><EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON>” <PERSON> <<EMAIL>>, 2016
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016
# <PERSON><PERSON><PERSON> Alves Feitosa Neto <<EMAIL>>, 2015
# <AUTHOR> <EMAIL>, 2012
# <AUTHOR> <EMAIL>, 2014
# <PERSON>, 2013
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2013
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2015
# g<PERSON><PERSON> dos <PERSON> al<PERSON> <<EMAIL>>, 2013
# fa9e10542e458baef0599ae856e43651_13d2225, 2012
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2011
# <AUTHOR> <EMAIL>, 2015
# <AUTHOR> <EMAIL>, 2022,2025
# <AUTHOR> <EMAIL>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-05-22 11:46-0300\n"
"PO-Revision-Date: 2025-03-19 11:30-0500\n"
"Last-Translator: Rafael Fontenelle <<EMAIL>>, 2022,2025\n"
"Language-Team: Portuguese (Brazil) (http://app.transifex.com/django/django/"
"language/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

msgid "Personal info"
msgstr "Informações pessoais"

msgid "Permissions"
msgstr "Permissões"

msgid "Important dates"
msgstr "Datas importantes"

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr "objeto %(name)s com chave primária %(key)r não existe."

msgid "Conflicting form data submitted. Please try again."
msgstr "Formulário com datos conflitantes. Por favor, tente novamente."

msgid "Password changed successfully."
msgstr "Senha modificada com sucesso."

msgid "Password-based authentication was disabled."
msgstr "Autenticação baseada em senha foi desabilitada."

#, python-format
msgid "Change password: %s"
msgstr "Alterar senha: %s"

#, python-format
msgid "Set password: %s"
msgstr "Configurar senha: %s"

msgid "Authentication and Authorization"
msgstr "Autenticação e Autorização"

msgid "password"
msgstr "senha"

msgid "last login"
msgstr "último login"

msgid "Invalid password format or unknown hashing algorithm."
msgstr "Formato de senha inválido ou algoritmo de hash desconhecido."

msgid "No password set."
msgstr "Nenhuma senha definida."

msgid "Reset password"
msgstr "Reconfigurar senha"

msgid "Set password"
msgstr "Configurar senha"

msgid "The two password fields didn’t match."
msgstr "Os dois campos de senha não correspondem."

msgid "Password"
msgstr "Senha"

msgid "Password confirmation"
msgstr "Confirmação de senha"

msgid "Enter the same password as before, for verification."
msgstr "Informe a mesma senha informada anteriormente, para verificação."

msgid ""
"Whether the user will be able to authenticate using a password or not. If "
"disabled, they may still be able to authenticate using other backends, such "
"as Single Sign-On or LDAP."
msgstr ""
"Se este usuário conseguirá autenticar usando uma senha ou não. Se "
"desabilitado ainda será possível autenticar utilizando outros métodos, como "
"Login Único ou LDAP."

msgid "Password-based authentication"
msgstr "Autenticação baseada em senha"

msgid "Enabled"
msgstr "Habilitado"

msgid "Disabled"
msgstr "Desabilitado"

msgid ""
"Raw passwords are not stored, so there is no way to see the user’s password."
msgstr ""
"Senhas não são armazenadas diretamente, então não é possível ver a senha do "
"usuário."

msgid ""
"Enable password-based authentication for this user by setting a password."
msgstr ""
"Habilitar autenticação baseada em senha para este usuário ao configurar uma "
"senha."

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""
"Por favor, entre com um %(username)s  e senha corretos. Note que ambos os "
"campos diferenciam maiúsculas e minúsculas."

msgid "This account is inactive."
msgstr "Esta conta está inativa."

msgid "Email"
msgstr "Email"

msgid "New password"
msgstr "Nova senha"

msgid "New password confirmation"
msgstr "Confirmação da nova senha"

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr ""
"A senha antiga foi digitada incorretamente. Por favor, informe-a novamente."

msgid "Old password"
msgstr "Senha antiga"

msgid "algorithm"
msgstr "algoritmo"

msgid "iterations"
msgstr "iterações"

msgid "salt"
msgstr "salt"

msgid "hash"
msgstr "hash"

msgid "variety"
msgstr "variedade"

msgid "version"
msgstr "versão"

msgid "memory cost"
msgstr "custo de memória"

msgid "time cost"
msgstr "custo de tempo"

msgid "parallelism"
msgstr "paralelismo"

msgid "work factor"
msgstr "fator de trabalho"

msgid "checksum"
msgstr "checksum"

msgid "block size"
msgstr "tamanho de bloco"

msgid "name"
msgstr "nome"

msgid "content type"
msgstr "tipo de conteúdo"

msgid "codename"
msgstr "apelido"

msgid "permission"
msgstr "permissão"

msgid "permissions"
msgstr "permissões"

msgid "group"
msgstr "grupo"

msgid "groups"
msgstr "grupos"

msgid "superuser status"
msgstr "status de superusuário"

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr ""
"Indica que este usuário tem todas as permissões sem atribuí-las "
"explicitamente."

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""
"Os grupos que este usuário pertence. Um usuário terá todas as permissões "
"concedidas a cada um dos seus grupos."

msgid "user permissions"
msgstr "permissões do usuário"

msgid "Specific permissions for this user."
msgstr "Permissões específicas para este usuário."

msgid "username"
msgstr "usuário"

msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr ""
"Obrigatório. 150 caracteres ou menos. Letras, números e @/./+/-/_ apenas."

msgid "A user with that username already exists."
msgstr "Um usuário com este nome de usuário já existe."

msgid "first name"
msgstr "primeiro nome"

msgid "last name"
msgstr "último nome"

msgid "email address"
msgstr "endereço de email"

msgid "staff status"
msgstr "membro da equipe"

msgid "Designates whether the user can log into this admin site."
msgstr "Indica que usuário consegue acessar este site de administração."

msgid "active"
msgstr "ativo"

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""
"Indica que o usuário será tratado como ativo. Ao invés de excluir contas de "
"usuário, desmarque isso."

msgid "date joined"
msgstr "data de registro"

msgid "user"
msgstr "usuário"

msgid "users"
msgstr "usuários"

#, python-format
msgid "This password is too short. It must contain at least %d character."
msgid_plural ""
"This password is too short. It must contain at least %d characters."
msgstr[0] ""
"Esta senha é muito curta. Ela precisa conter pelo menos %d caractere."
msgstr[1] ""
"Esta senha é muito curta. Ela precisa conter pelo menos %d caracteres."
msgstr[2] ""
"Esta senha é muito curta. Ela precisa conter pelo menos %d caracteres."

#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] "Sua senha precisa conter pelo menos %(min_length)d caracteres."
msgstr[1] "Sua senha precisa conter pelo menos %(min_length)d caracteres."
msgstr[2] "Sua senha precisa conter pelo menos %(min_length)d caracteres."

#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr "A senha é muito parecida com %(verbose_name)s"

msgid "Your password can’t be too similar to your other personal information."
msgstr ""
"Sua senha não pode ser muito parecida com o resto das suas informações "
"pessoais."

msgid "This password is too common."
msgstr "Esta senha é muito comum."

msgid "Your password can’t be a commonly used password."
msgstr "Sua senha não pode ser uma senha comumente utilizada."

msgid "This password is entirely numeric."
msgstr "Esta senha é inteiramente numérica."

msgid "Your password can’t be entirely numeric."
msgstr "Sua senha não pode ser inteiramente numérica."

#, python-format
msgid "Password reset on %(site_name)s"
msgstr "Redefinição de senha em %(site_name)s "

msgid ""
"Enter a valid username. This value may contain only unaccented lowercase a-z "
"and uppercase A-Z letters, numbers, and @/./+/-/_ characters."
msgstr ""
"Insira um nome de usuário válido. Este valor pode conter apenas letras, "
"números e os caracteres @/./+/-/_."

msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""
"Informe um nome de usuário válido. Este valor pode conter apenas letras, "
"números e os seguintes caracteres @/./+/-/_."

msgid "Logged out"
msgstr "Sessão encerrada"

msgid "Password reset"
msgstr "Redefinição de senha"

msgid "Password reset sent"
msgstr "Redefinição de senha enviada"

msgid "Enter new password"
msgstr "Digite a nova senha"

msgid "Password reset unsuccessful"
msgstr "Redefinição de senha sem sucesso"

msgid "Password reset complete"
msgstr "Redefinição de senha completa"

msgid "Password change"
msgstr "Alteração de Senha"

msgid "Password change successful"
msgstr "Mudança de senha bem sucedida"
