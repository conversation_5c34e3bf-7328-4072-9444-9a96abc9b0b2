{% extends 'base.html' %}
{% load static %}

{% block title %}Fashion Store - Trendy Fashion for Everyone{% endblock %}

{% block content %}
<!-- Responsive Hero Section -->
<section class="hero-section">
    <div class="container">
        <!-- Mobile Hero Design -->
        <div class="hero-mobile-design">
            <div class="text-center">
                <div class="mb-4">
                    <i class="fas fa-tshirt fa-4x mb-3" style="opacity: 0.9;"></i>
                </div>
                <h1 class="fw-bold mb-3">Fashion Store</h1>
                <p class="mb-4">Trendy styles, unbeatable prices</p>
                <div class="d-flex flex-column gap-2">
                    <a href="{% url 'store:product_list' %}" class="btn btn-light btn-lg">
                        <i class="fas fa-shopping-bag me-2"></i>Start Shopping
                    </a>
                </div>
            </div>
        </div>

        <!-- Desktop Hero Design -->
        <div class="hero-desktop-design">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="fw-bold mb-4">Discover Your Style</h1>
                    <p class="mb-4">Explore our curated collection of trendy and fashionable clothing. From casual wear to formal attire, find everything you need to express your unique style.</p>
                    <div class="d-flex gap-3">
                        <a href="{% url 'store:product_list' %}" class="btn btn-primary btn-lg">
                            <i class="fas fa-shopping-bag me-2"></i>Shop Now
                        </a>
                        <a href="#featured" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-eye me-2"></i>View Collection
                        </a>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <div class="hero-image-placeholder">
                        <i class="fas fa-tshirt fa-5x mb-3" style="opacity: 0.7;"></i>
                        <p class="mb-0" style="opacity: 0.8;">Fashion Collection</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Responsive Categories Section -->
<section class="category-section">
    <div class="container">
        <div class="section-title">
            <h2>Shop by Category</h2>
            <p class="d-none d-md-block">Discover our wide range of fashion categories</p>
        </div>

        <!-- Mobile Category Design -->
        <div class="mobile-category-grid d-md-none">
            {% for category in categories %}
            <a href="{{ category.get_absolute_url }}" class="mobile-category-card text-decoration-none">
                <div class="mobile-category-icon">
                    <i class="fas fa-tshirt"></i>
                </div>
                <div class="mobile-category-title">{{ category.name }}</div>
                <div class="mobile-category-count">{{ category.products.count }} items</div>
                <button class="mobile-category-btn">Shop Now</button>
            </a>
            {% empty %}
            <div style="grid-column: 1 / -1;" class="text-center">
                <p class="text-muted">No categories available</p>
            </div>
            {% endfor %}
        </div>

        <!-- Desktop Category Design -->
        <div class="desktop-category-layout d-none d-md-block">
            <div class="row">
                {% for category in categories %}
                <div class="col-md-4 col-lg-4 mb-4">
                    <div class="category-card h-100">
                        {% if category.image %}
                        <img src="{{ category.image.url }}" alt="{{ category.name }}" class="img-fluid rounded mb-3" style="height: 150px; width: 100%; object-fit: cover;">
                        {% else %}
                        <i class="fas fa-tshirt fa-3x mb-3"></i>
                        {% endif %}
                        <h5 class="fw-bold">{{ category.name }}</h5>
                        <p class="text-muted small">{{ category.description|truncatewords:10 }}</p>
                        <a href="{{ category.get_absolute_url }}" class="btn btn-outline-primary btn-sm w-100">
                            <i class="fas fa-arrow-right me-1"></i>Explore
                        </a>
                    </div>
                </div>
                {% empty %}
                <div class="col-12 text-center">
                    <p class="text-muted">No categories available at the moment.</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<section class="featured-section" id="featured">
    <div class="container">
        <div class="section-title">
            <h2>Featured Products</h2>
            <p>Handpicked items that are trending right now</p>
        </div>
        <!-- Mobile Product Design -->
        <div class="d-md-none">
            {% for product in featured_products %}
            <div class="product-card mb-3">
                <div class="mobile-product-layout">
                    <div class="mobile-product-image">
                        {% if product.image %}
                        <img src="{{ product.image.url }}" alt="{{ product.name }}">
                        {% else %}
                        <i class="fas fa-image text-muted"></i>
                        {% endif %}
                    </div>
                    <div class="mobile-product-content">
                        <div class="mobile-product-title">{{ product.name }}</div>
                        <div class="mobile-product-category">{{ product.category.name }}</div>
                        <div class="mobile-product-price">
                            {% if product.discount_price %}
                            <span class="original-price">${{ product.price }}</span>
                            {% endif %}
                            ${{ product.get_price }}
                        </div>
                        <div class="mobile-product-actions">
                            <a href="{{ product.get_absolute_url }}" class="mobile-view-btn">
                                <i class="fas fa-eye"></i>View
                            </a>
                            <button class="mobile-add-btn add-to-cart" data-product-id="{{ product.id }}">
                                <i class="fas fa-plus"></i>Add
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="text-center py-5">
                <i class="fas fa-star fa-3x text-muted mb-3"></i>
                <h4>No Featured Products</h4>
                <p class="text-muted">Check back soon!</p>
            </div>
            {% endfor %}
        </div>

        <!-- Desktop Product Design -->
        <div class="row d-none d-md-flex">
            {% for product in featured_products %}
            <div class="col-md-4 col-lg-3 mb-4">
                <div class="card product-card h-100 desktop-product-layout">
                    {% if product.get_discount_percentage %}
                    <span class="badge">{{ product.get_discount_percentage }}% OFF</span>
                    {% endif %}
                    <button class="quick-add-btn add-to-cart" data-product-id="{{ product.id }}" title="Quick Add">
                        <i class="fas fa-plus"></i>
                    </button>
                    {% if product.image %}
                    <img src="{{ product.image.url }}" class="card-img-top" alt="{{ product.name }}">
                    {% else %}
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                        <i class="fas fa-image text-muted fa-2x"></i>
                    </div>
                    {% endif %}
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title">{{ product.name }}</h6>
                        <p class="card-text text-muted small flex-grow-1">{{ product.description|truncatewords:8 }}</p>
                        <div class="product-price mb-3">
                            {% if product.discount_price %}
                            <span class="original-price">${{ product.price }}</span>
                            {% endif %}
                            ${{ product.get_price }}
                        </div>
                        <div class="product-actions mt-auto">
                            <a href="{{ product.get_absolute_url }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>View
                            </a>
                            <button class="btn btn-primary btn-sm add-to-cart" data-product-id="{{ product.id }}">
                                <i class="fas fa-cart-plus me-1"></i>Add
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12 text-center py-5">
                <i class="fas fa-star fa-3x text-muted mb-3"></i>
                <h4>No Featured Products</h4>
                <p class="text-muted">Check back soon for our featured items!</p>
            </div>
            {% endfor %}
        </div>
        <div class="text-center mt-4">
            <a href="{% url 'store:product_list' %}" class="btn btn-primary btn-lg">View All Products</a>
        </div>
    </div>
</section>

<!-- Latest Products Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="section-title">
            <h2>Latest Arrivals</h2>
            <p>Check out our newest additions to the collection</p>
        </div>
        <div class="row">
            {% for product in latest_products %}
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card product-card">
                    <span class="badge bg-success">New</span>
                    {% if product.image %}
                    <img src="{{ product.image.url }}" class="card-img-top" alt="{{ product.name }}">
                    {% else %}
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                        <i class="fas fa-image text-muted fa-3x"></i>
                    </div>
                    {% endif %}
                    <div class="card-body">
                        <h5 class="card-title">{{ product.name }}</h5>
                        <p class="card-text text-muted">{{ product.description|truncatewords:10 }}</p>
                        <div class="product-price mb-3">
                            ${{ product.get_price }}
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="{{ product.get_absolute_url }}" class="btn btn-outline-primary btn-sm">View Details</a>
                            <button class="btn btn-primary btn-sm add-to-cart" data-product-id="{{ product.id }}">
                                <i class="fas fa-cart-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12 text-center">
                <p class="text-muted">No new products available at the moment.</p>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h3 class="fw-bold mb-3">Stay Updated with Fashion Trends</h3>
                <p class="mb-0">Subscribe to our newsletter and get exclusive offers, style tips, and early access to new collections.</p>
            </div>
            <div class="col-lg-6">
                <form class="d-flex gap-2">
                    <input type="email" class="form-control" placeholder="Enter your email address" required>
                    <button type="submit" class="btn btn-light">Subscribe</button>
                </form>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    // Add CSRF token for AJAX requests
    const csrfToken = '{{ csrf_token }}';
    document.querySelector('meta[name="csrf-token"]')?.setAttribute('content', csrfToken);
</script>
{% endblock %}
