{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrahead %}{{ block.super }}
<style>
    body {
        background: linear-gradient(135deg, #6c63ff 0%, #ff6b6b 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'Inter', sans-serif;
    }
    
    #header, #footer, .breadcrumbs {
        display: none !important;
    }
    
    #content {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        padding: 0;
        margin: 0;
        width: 100%;
        max-width: 400px;
        overflow: hidden;
    }
    
    .login-header {
        background: linear-gradient(135deg, #6c63ff, #ff6b6b);
        color: white;
        text-align: center;
        padding: 40px 30px 30px;
    }
    
    .login-header h1 {
        font-size: 2rem;
        font-weight: 700;
        margin: 0 0 10px 0;
    }
    
    .login-header p {
        margin: 0;
        opacity: 0.9;
        font-size: 1rem;
    }
    
    .login-form {
        padding: 40px 30px;
    }
    
    .form-row {
        margin-bottom: 25px;
    }
    
    .form-row label {
        display: block;
        margin-bottom: 8px;
        color: #495057;
        font-weight: 600;
        font-size: 14px;
    }
    
    .form-row input[type="text"],
    .form-row input[type="password"] {
        width: 100%;
        padding: 15px 20px;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        font-size: 16px;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }
    
    .form-row input:focus {
        outline: none;
        border-color: #6c63ff;
        background: white;
        box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.1);
    }
    
    .submit-row {
        margin-top: 30px;
    }
    
    .submit-row input[type="submit"] {
        width: 100%;
        background: linear-gradient(135deg, #6c63ff, #ff6b6b);
        color: white;
        border: none;
        padding: 15px;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .submit-row input[type="submit"]:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(108, 99, 255, 0.3);
    }
    
    .errornote {
        background: #ff6b6b;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        font-size: 14px;
    }
    
    .login-footer {
        text-align: center;
        padding: 20px 30px;
        background: #f8f9fa;
        color: #6c757d;
        font-size: 14px;
    }
    
    .icon {
        font-size: 3rem;
        margin-bottom: 20px;
        opacity: 0.9;
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-header">
        <i class="fas fa-tshirt icon"></i>
        <h1>Fashion Store</h1>
        <p>Admin Dashboard</p>
    </div>
    
    <div class="login-form">
        {% if form.errors and not form.non_field_errors %}
            <div class="errornote">
                Please correct the errors below.
            </div>
        {% endif %}
        
        {% if form.non_field_errors %}
            <div class="errornote">
                {{ form.non_field_errors }}
            </div>
        {% endif %}
        
        <form action="{{ app_path }}" method="post" id="login-form">
            {% csrf_token %}
            
            <div class="form-row">
                <label for="id_username">Username:</label>
                {{ form.username }}
                {% if form.username.errors %}
                    <div class="errornote">{{ form.username.errors }}</div>
                {% endif %}
            </div>
            
            <div class="form-row">
                <label for="id_password">Password:</label>
                {{ form.password }}
                {% if form.password.errors %}
                    <div class="errornote">{{ form.password.errors }}</div>
                {% endif %}
            </div>
            
            <div class="submit-row">
                <input type="submit" value="Sign In" />
                <input type="hidden" name="next" value="{{ next }}" />
            </div>
        </form>
    </div>
    
    <div class="login-footer">
        <p>&copy; 2024 Fashion Store. All rights reserved.</p>
    </div>
</div>
{% endblock %}
