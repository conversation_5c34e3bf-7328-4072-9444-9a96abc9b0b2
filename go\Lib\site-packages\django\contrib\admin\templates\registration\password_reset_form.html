{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}{% if form.email.errors %}{% translate "Error:" %} {% endif %}{{ block.super }}{% endblock %}
{% block extrastyle %}{{ block.super }}<link rel="stylesheet" href="{% static "admin/css/forms.css" %}">{% endblock %}
{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
&rsaquo; {% translate 'Password reset' %}
</div>
{% endblock %}

{% block content %}

<p>{% translate 'Forgotten your password? Enter your email address below, and we’ll email instructions for setting a new one.' %}</p>

<form method="post">{% csrf_token %}
  <fieldset class="module aligned">
    <div class="form-row field-email">
      {{ form.email.errors }}
      <div class="flex-container">
        <label for="id_email">{% translate 'Email address:' %}</label>
        {{ form.email }}
      </div>
    </div>
  </fieldset>
  <div class="submit-row">
    <input type="submit" value="{% translate 'Reset my password' %}">
  </div>
</form>

{% endblock %}
