{% extends 'base.html' %}
{% load static %}

{% block title %}{{ category.name }} - Fashion Store{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Category Header -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            {% if category.image %}
            <img src="{{ category.image.url }}" class="img-fluid rounded mb-3" style="max-height: 200px; object-fit: cover;" alt="{{ category.name }}">
            {% endif %}
            <h1 class="display-4 fw-bold">{{ category.name }}</h1>
            {% if category.description %}
            <p class="lead text-muted">{{ category.description }}</p>
            {% endif %}
        </div>
    </div>
    
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'store:home' %}">Home</a></li>
            <li class="breadcrumb-item"><a href="{% url 'store:product_list' %}">Products</a></li>
            <li class="breadcrumb-item active">{{ category.name }}</li>
        </ol>
    </nav>
    
    <div class="row">
        <!-- Products -->
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h3>Products</h3>
                    <p class="text-muted mb-0">{{ page_obj.paginator.count }} products found</p>
                </div>
                
                <!-- Sort Options -->
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        Sort by
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item {% if current_sort == 'name' %}active{% endif %}" href="?sort=name">Name</a></li>
                        <li><a class="dropdown-item {% if current_sort == 'price_low' %}active{% endif %}" href="?sort=price_low">Price: Low to High</a></li>
                        <li><a class="dropdown-item {% if current_sort == 'price_high' %}active{% endif %}" href="?sort=price_high">Price: High to Low</a></li>
                        <li><a class="dropdown-item {% if current_sort == 'newest' %}active{% endif %}" href="?sort=newest">Newest</a></li>
                    </ul>
                </div>
            </div>
            
            <!-- Product Grid -->
            <div class="row">
                {% for product in page_obj %}
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card product-card h-100">
                        {% if product.get_discount_percentage %}
                        <span class="badge bg-danger">{{ product.get_discount_percentage }}% OFF</span>
                        {% endif %}
                        {% if product.image %}
                        <img src="{{ product.image.url }}" class="card-img-top" alt="{{ product.name }}">
                        {% else %}
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                            <i class="fas fa-image text-muted fa-3x"></i>
                        </div>
                        {% endif %}
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">{{ product.name }}</h5>
                            <p class="card-text text-muted flex-grow-1">{{ product.description|truncatewords:15 }}</p>
                            <div class="product-price mb-3">
                                {% if product.discount_price %}
                                <span class="original-price">${{ product.price }}</span>
                                {% endif %}
                                ${{ product.get_price }}
                            </div>
                            <div class="d-flex justify-content-between align-items-center mt-auto">
                                <a href="{{ product.get_absolute_url }}" class="btn btn-outline-primary btn-sm">View Details</a>
                                <button class="btn btn-primary btn-sm add-to-cart" data-product-id="{{ product.id }}">
                                    <i class="fas fa-cart-plus"></i> Add to Cart
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12 text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4>No products found</h4>
                    <p class="text-muted">This category doesn't have any products yet.</p>
                    <a href="{% url 'store:product_list' %}" class="btn btn-primary">View All Products</a>
                </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Product pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if current_sort %}&sort={{ current_sort }}{% endif %}">Previous</a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if current_sort %}&sort={{ current_sort }}{% endif %}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if current_sort %}&sort={{ current_sort }}{% endif %}">Next</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add CSRF token for AJAX requests
    const csrfToken = '{{ csrf_token }}';
</script>
{% endblock %}
