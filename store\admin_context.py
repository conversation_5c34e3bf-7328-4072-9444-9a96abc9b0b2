from django.contrib.auth.models import User
from django.db.models import Sum
from .models import Product, Order, Category

def admin_dashboard_context(request):
    """
    Context processor for admin dashboard statistics
    """
    if not request.path.startswith('/admin/'):
        return {}
    
    # Calculate statistics
    total_products = Product.objects.filter(is_active=True).count()
    total_orders = Order.objects.count()
    total_users = User.objects.count()
    total_revenue = Order.objects.aggregate(
        total=Sum('total_amount')
    )['total'] or 0
    
    # Recent orders
    recent_orders = Order.objects.select_related('user').order_by('-created_at')[:5]
    
    return {
        'total_products': total_products,
        'total_orders': total_orders,
        'total_users': total_users,
        'total_revenue': total_revenue,
        'recent_orders': recent_orders,
    }
