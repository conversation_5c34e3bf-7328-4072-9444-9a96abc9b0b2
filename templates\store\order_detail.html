{% extends 'base.html' %}
{% load static %}

{% block title %}Order {{ order.order_number }} - Fashion Store{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <!-- Order Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>Order {{ order.order_number }}</h2>
                    <p class="text-muted mb-0">Placed on {{ order.created_at|date:"F d, Y" }} at {{ order.created_at|time:"g:i A" }}</p>
                </div>
                <div>
                    <span class="badge bg-{% if order.status == 'delivered' %}success{% elif order.status == 'shipped' %}info{% elif order.status == 'processing' %}warning{% elif order.status == 'cancelled' %}danger{% else %}secondary{% endif %} fs-6">
                        {{ order.get_status_display }}
                    </span>
                </div>
            </div>
            
            <!-- Order Success Message -->
            <div class="alert alert-success d-flex align-items-center mb-4">
                <i class="fas fa-check-circle fa-2x me-3"></i>
                <div>
                    <h5 class="alert-heading mb-1">Order Confirmed!</h5>
                    <p class="mb-0">Thank you for your purchase. We'll send you a confirmation email shortly.</p>
                </div>
            </div>
            
            <div class="row">
                <!-- Order Items -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Order Items</h5>
                        </div>
                        <div class="card-body">
                            {% for item in order.items.all %}
                            <div class="row align-items-center {% if not forloop.last %}border-bottom pb-3 mb-3{% endif %}">
                                <div class="col-md-2">
                                    {% if item.product.image %}
                                    <img src="{{ item.product.image.url }}" class="img-fluid rounded" alt="{{ item.product.name }}">
                                    {% else %}
                                    <div class="bg-light d-flex align-items-center justify-content-center rounded" style="height: 80px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <h6 class="mb-1">{{ item.product.name }}</h6>
                                    <p class="text-muted mb-1">{{ item.product.category.name }}</p>
                                    {% if item.size %}
                                    <small class="text-muted">Size: {{ item.size }}</small>
                                    {% endif %}
                                </div>
                                <div class="col-md-2 text-center">
                                    <span>Qty: {{ item.quantity }}</span>
                                </div>
                                <div class="col-md-2 text-end">
                                    <span class="fw-bold">${{ item.get_total_price }}</span>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <!-- Shipping Information -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Shipping Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Shipping Address</h6>
                                    <address>
                                        {{ order.first_name }} {{ order.last_name }}<br>
                                        {{ order.address }}<br>
                                        {{ order.city }}, {{ order.postal_code }}<br>
                                        {{ order.country }}
                                    </address>
                                </div>
                                <div class="col-md-6">
                                    <h6>Contact Information</h6>
                                    <p class="mb-1"><strong>Email:</strong> {{ order.email }}</p>
                                    <p class="mb-0"><strong>Phone:</strong> {{ order.phone }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Order Summary -->
                <div class="col-lg-4">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Order Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Subtotal:</span>
                                <span>${{ order.total_amount }}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Shipping:</span>
                                <span>Free</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Tax:</span>
                                <span>$0.00</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between">
                                <strong>Total:</strong>
                                <strong>${{ order.total_amount }}</strong>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Order Status -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Order Status</h5>
                        </div>
                        <div class="card-body">
                            <div class="order-timeline">
                                <div class="timeline-item {% if order.status == 'pending' or order.status == 'processing' or order.status == 'shipped' or order.status == 'delivered' %}completed{% endif %}">
                                    <div class="timeline-marker"></div>
                                    <div class="timeline-content">
                                        <h6>Order Placed</h6>
                                        <small class="text-muted">{{ order.created_at|date:"M d, Y g:i A" }}</small>
                                    </div>
                                </div>
                                
                                <div class="timeline-item {% if order.status == 'processing' or order.status == 'shipped' or order.status == 'delivered' %}completed{% endif %}">
                                    <div class="timeline-marker"></div>
                                    <div class="timeline-content">
                                        <h6>Processing</h6>
                                        <small class="text-muted">{% if order.status != 'pending' %}{{ order.updated_at|date:"M d, Y g:i A" }}{% else %}Pending{% endif %}</small>
                                    </div>
                                </div>
                                
                                <div class="timeline-item {% if order.status == 'shipped' or order.status == 'delivered' %}completed{% endif %}">
                                    <div class="timeline-marker"></div>
                                    <div class="timeline-content">
                                        <h6>Shipped</h6>
                                        <small class="text-muted">{% if order.status == 'shipped' or order.status == 'delivered' %}{{ order.updated_at|date:"M d, Y g:i A" }}{% else %}Pending{% endif %}</small>
                                    </div>
                                </div>
                                
                                <div class="timeline-item {% if order.status == 'delivered' %}completed{% endif %}">
                                    <div class="timeline-marker"></div>
                                    <div class="timeline-content">
                                        <h6>Delivered</h6>
                                        <small class="text-muted">{% if order.status == 'delivered' %}{{ order.updated_at|date:"M d, Y g:i A" }}{% else %}Pending{% endif %}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Actions -->
                    <div class="card">
                        <div class="card-body">
                            <a href="{% url 'store:order_history' %}" class="btn btn-outline-primary w-100 mb-2">
                                <i class="fas fa-list me-2"></i>View All Orders
                            </a>
                            <a href="{% url 'store:product_list' %}" class="btn btn-primary w-100">
                                <i class="fas fa-shopping-bag me-2"></i>Continue Shopping
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .order-timeline {
        position: relative;
    }
    
    .timeline-item {
        position: relative;
        padding-left: 30px;
        margin-bottom: 20px;
    }
    
    .timeline-item:not(:last-child)::before {
        content: '';
        position: absolute;
        left: 8px;
        top: 20px;
        bottom: -20px;
        width: 2px;
        background-color: #e9ecef;
    }
    
    .timeline-item.completed:not(:last-child)::before {
        background-color: var(--primary-color);
    }
    
    .timeline-marker {
        position: absolute;
        left: 0;
        top: 5px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #e9ecef;
        border: 2px solid #fff;
    }
    
    .timeline-item.completed .timeline-marker {
        background-color: var(--primary-color);
    }
    
    .timeline-content h6 {
        margin-bottom: 2px;
        font-size: 0.9rem;
    }
    
    .timeline-content small {
        font-size: 0.8rem;
    }
</style>
{% endblock %}
