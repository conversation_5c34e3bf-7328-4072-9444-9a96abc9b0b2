# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2022
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2023-04-24 19:22+0000\n"
"Last-Translator: Swara <<EMAIL>>, 2022\n"
"Language-Team: Central Kurdish (http://www.transifex.com/django/django/"
"language/ckb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ckb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "جۆرەکانی ناوەڕۆک"

msgid "python model class name"
msgstr "ناوی پۆلی مۆدێلی پایسۆن"

msgid "content type"
msgstr "جۆری ناوەڕۆک"

msgid "content types"
msgstr "جۆرەکانی ناوەڕۆک"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "ئۆبجێکی جۆری ناوەڕۆکی %(ct_id)s هیچ مۆدێلێکی پەیوەستکراوی نییە"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "جۆری ناوەڕۆکی %(ct_id)s ئۆبجێکتی %(obj_id)s بوونی نیە"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "ئۆبجێکتەکانی %(ct_name)s ڕێچکەی get_absolute_url() ی نیە"
