/* Custom CSS for Fashion Store */

:root {
    --primary-color: #6c63ff;
    --secondary-color: #ff6b6b;
    --accent-color: #4ecdc4;
    --dark-color: #2c3e50;
    --light-color: #f8f9fa;
    --text-color: #333;
    --border-color: #e9ecef;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #fff;
}

/* Navigation Styles */
.navbar-brand {
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--text-color) !important;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 100px 0;
    text-align: center;
}

.hero-section h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Button Styles */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
    padding: 12px 30px;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #5a52d5;
    border-color: #5a52d5;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108, 99, 255, 0.3);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
    padding: 12px 30px;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Card Styles */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.card-img-top {
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* Product Card Styles */
.product-card {
    position: relative;
    margin-bottom: 30px;
}

.product-card .badge {
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 2;
    font-size: 0.8rem;
    padding: 5px 10px;
}

.product-price {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

.product-price .original-price {
    text-decoration: line-through;
    color: #999;
    font-size: 1rem;
    margin-right: 10px;
}

/* Category Section */
.category-section {
    padding: 80px 0;
    background-color: var(--light-color);
}

.category-card {
    text-align: center;
    padding: 40px 20px;
    background: white;
    border-radius: 15px;
    transition: all 0.3s ease;
    height: 100%;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.category-card i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

/* Featured Products */
.featured-section {
    padding: 80px 0;
}

.section-title {
    text-align: center;
    margin-bottom: 60px;
}

.section-title h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 15px;
}

.section-title p {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Cart Styles */
.cart-item {
    border-bottom: 1px solid var(--border-color);
    padding: 20px 0;
}

.cart-item:last-child {
    border-bottom: none;
}

.quantity-input {
    width: 80px;
    text-align: center;
}

/* Form Styles */
.form-control {
    border-radius: 10px;
    border: 2px solid var(--border-color);
    padding: 12px 15px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(108, 99, 255, 0.25);
}

/* Footer Styles */
footer {
    background: linear-gradient(135deg, #2c3e50, #34495e) !important;
}

footer .social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

footer .social-links a:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .hero-section p {
        font-size: 1rem;
    }
    
    .section-title h2 {
        font-size: 2rem;
    }
    
    .card-img-top {
        height: 200px;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}
