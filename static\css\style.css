/* Mobile-First Responsive CSS for Fashion Store */

:root {
    --primary-color: #6c63ff;
    --secondary-color: #ff6b6b;
    --accent-color: #4ecdc4;
    --success-color: #51cf66;
    --warning-color: #ffd43b;
    --danger-color: #ff6b6b;
    --dark-color: #2c3e50;
    --light-color: #f8f9fa;
    --text-color: #333;
    --border-color: #e9ecef;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 20px;
    --border-radius-xl: 25px;
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #fff;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Mobile-first container */
.container {
    width: 100%;
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
    margin-left: auto;
    margin-right: auto;
}

/* Responsive breakpoints */
@media (min-width: 576px) {
    .container { max-width: 540px; }
}

@media (min-width: 768px) {
    .container {
        max-width: 720px;
        padding-left: var(--spacing-lg);
        padding-right: var(--spacing-lg);
    }
}

@media (min-width: 992px) {
    .container { max-width: 960px; }
}

@media (min-width: 1200px) {
    .container { max-width: 1140px; }
}

@media (min-width: 1400px) {
    .container { max-width: 1320px; }

/* Mobile-First Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: var(--spacing-sm) 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98) !important;
    box-shadow: var(--shadow-light);
    padding: var(--spacing-xs) 0;
}

.navbar-brand {
    font-weight: 800;
    font-size: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none !important;
}

.navbar-brand i {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Mobile hamburger menu */
.navbar-toggler {
    border: none;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    background: var(--light-color);
    transition: all 0.3s ease;
}

.navbar-toggler:focus {
    box-shadow: none;
    background: var(--primary-color);
}

.navbar-toggler:focus .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Mobile navigation menu */
.navbar-collapse {
    background: white;
    border-radius: var(--border-radius-lg);
    margin-top: var(--spacing-md);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-medium);
    border: 1px solid var(--border-color);
}

.navbar-nav {
    gap: var(--spacing-sm);
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--text-color) !important;
    padding: var(--spacing-md) var(--spacing-lg) !important;
    border-radius: var(--border-radius-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(108, 99, 255, 0.1), transparent);
    transition: left 0.5s;
}

.navbar-nav .nav-link:hover::before {
    left: 100%;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
    color: var(--primary-color) !important;
    background: rgba(108, 99, 255, 0.05);
    transform: translateY(-2px);
}

/* Cart badge */
.cart-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, var(--secondary-color), #ff5252);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 11px;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Enhanced Mobile Bottom Navigation */
.mobile-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 255, 0.98) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-top: 1px solid rgba(108, 99, 255, 0.1);
    padding: var(--spacing-sm) 0;
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 1020;
    box-shadow: 0 -4px 24px rgba(0, 0, 0, 0.08);
}

@media (max-width: 767px) {
    .mobile-bottom-nav {
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 249, 255, 1) 100%);
        border-top: 2px solid rgba(108, 99, 255, 0.15);
        padding: 12px 0 16px;
    }
}

.bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--text-color);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    min-width: 60px;
}

.bottom-nav-item i {
    font-size: 1.2rem;
    margin-bottom: 2px;
    transition: all 0.3s ease;
}

.bottom-nav-item span {
    font-size: 0.7rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.bottom-nav-item.active {
    color: var(--primary-color);
    transform: translateY(-2px);
}

.bottom-nav-item.active i {
    transform: scale(1.1);
}

.bottom-nav-badge {
    position: absolute;
    top: -2px;
    right: 8px;
    background: var(--secondary-color);
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    font-size: 10px;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Desktop navigation adjustments */
@media (min-width: 992px) {
    .navbar-collapse {
        background: transparent;
        border-radius: 0;
        margin-top: 0;
        padding: 0;
        box-shadow: none;
        border: none;
    }

    .navbar-nav {
        flex-direction: row;
        align-items: center;
    }

    .navbar-nav .nav-link {
        padding: var(--spacing-sm) var(--spacing-md) !important;
        margin: 0 var(--spacing-xs);
    }

    main {
        margin-bottom: 0 !important;
    }
}

/* Mobile-First Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: var(--spacing-xl) 0;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 50vh;
    display: flex;
    align-items: center;
}

/* Mobile-specific hero design */
@media (max-width: 767px) {
    .hero-section {
        background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        min-height: 45vh;
        padding: var(--spacing-lg) 0;
    }

    .hero-section::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        right: 0;
        height: 30px;
        background: white;
        border-radius: 20px 20px 0 0;
    }

    .hero-mobile-design {
        display: block;
    }

    .hero-desktop-design {
        display: none;
    }
}

@media (min-width: 768px) {
    .hero-mobile-design {
        display: none;
    }

    .hero-desktop-design {
        display: block;
    }
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-section h1 {
    font-size: 2rem;
    font-weight: 800;
    margin-bottom: var(--spacing-md);
    line-height: 1.2;
}

.hero-section p {
    font-size: 1rem;
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
    line-height: 1.6;
}

.hero-section .btn {
    margin: var(--spacing-xs);
    padding: var(--spacing-md) var(--spacing-xl);
    font-weight: 600;
    border-radius: var(--border-radius-xl);
}

/* Tablet hero adjustments */
@media (min-width: 768px) {
    .hero-section {
        padding: var(--spacing-xxl) 0;
        min-height: 70vh;
    }

    .hero-section h1 {
        font-size: 2.5rem;
    }

    .hero-section p {
        font-size: 1.1rem;
    }
}

/* Desktop hero adjustments */
@media (min-width: 992px) {
    .hero-section {
        padding: 120px 0;
        min-height: 80vh;
    }

    .hero-section h1 {
        font-size: 3.5rem;
    }

    .hero-section p {
        font-size: 1.2rem;
    }
}

/* Mobile-First Button Styles */
.btn {
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: var(--border-radius-xl);
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 0.9rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    min-height: 44px; /* Touch-friendly minimum */
    min-width: 44px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #5a52d5);
    border-color: var(--primary-color);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 99, 255, 0.3);
}

.btn-primary:hover,
.btn-primary:focus {
    background: linear-gradient(135deg, #5a52d5, var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(108, 99, 255, 0.4);
    color: white;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover,
.btn-outline-primary:focus {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(108, 99, 255, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--secondary-color), #ff5252);
    border-color: var(--secondary-color);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.btn-secondary:hover,
.btn-secondary:focus {
    background: linear-gradient(135deg, #ff5252, var(--secondary-color));
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #40c057);
    border-color: var(--success-color);
    color: white;
    box-shadow: 0 4px 15px rgba(81, 207, 102, 0.3);
}

.btn-success:hover,
.btn-success:focus {
    background: linear-gradient(135deg, #40c057, var(--success-color));
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(81, 207, 102, 0.4);
    color: white;
}

/* Button sizes */
.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.8rem;
    min-height: 36px;
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: 1.1rem;
    min-height: 52px;
}

/* Tablet button adjustments */
@media (min-width: 768px) {
    .btn {
        padding: var(--spacing-md) var(--spacing-xl);
        font-size: 1rem;
    }

    .btn-lg {
        padding: var(--spacing-lg) var(--spacing-xxl);
        font-size: 1.2rem;
    }
}

/* Mobile-First Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-light);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    background: white;
    margin-bottom: var(--spacing-lg);
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-heavy);
}

.card-img-top {
    height: 200px;
    object-fit: cover;
    transition: transform 0.4s ease;
    width: 100%;
}

.card:hover .card-img-top {
    transform: scale(1.08);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
    line-height: 1.3;
}

.card-text {
    font-size: 0.9rem;
    color: var(--text-color);
    line-height: 1.5;
    margin-bottom: var(--spacing-md);
}

.card-header {
    background: linear-gradient(135deg, var(--light-color), #e9ecef);
    border-bottom: none;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0 !important;
    padding: var(--spacing-lg);
    font-weight: 600;
    color: var(--dark-color);
}

/* Tablet card adjustments */
@media (min-width: 768px) {
    .card-img-top {
        height: 220px;
    }

    .card-title {
        font-size: 1.2rem;
    }

    .card-text {
        font-size: 1rem;
    }
}

/* Desktop card adjustments */
@media (min-width: 992px) {
    .card-img-top {
        height: 250px;
    }

    .card:hover {
        transform: translateY(-10px);
    }
}

/* Mobile-First Product Card Styles */
.product-card {
    position: relative;
    margin-bottom: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    background: white;
    box-shadow: var(--shadow-light);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mobile-specific product card design */
@media (max-width: 767px) {
    .product-card {
        border-radius: 20px;
        margin-bottom: var(--spacing-md);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
    }

    .product-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        z-index: 2;
    }

    .mobile-product-layout {
        display: flex;
        padding: 15px;
        gap: 15px;
        align-items: center;
    }

    .mobile-product-image {
        width: 80px;
        height: 80px;
        border-radius: 12px;
        overflow: hidden;
        flex-shrink: 0;
        background: var(--light-color);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .mobile-product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .mobile-product-content {
        flex: 1;
        min-width: 0;
    }

    .mobile-product-title {
        font-size: 1rem;
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 4px;
        line-height: 1.3;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .mobile-product-category {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 8px;
    }

    .mobile-product-price {
        font-size: 1.1rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 8px;
    }

    .mobile-product-actions {
        display: flex;
        gap: 8px;
    }

    .mobile-add-btn {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        border-radius: 20px;
        padding: 8px 16px;
        font-size: 0.8rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 4px;
        transition: all 0.3s ease;
    }

    .mobile-add-btn:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(108, 99, 255, 0.3);
    }

    .mobile-view-btn {
        background: transparent;
        color: var(--primary-color);
        border: 2px solid var(--primary-color);
        border-radius: 20px;
        padding: 6px 12px;
        font-size: 0.8rem;
        font-weight: 600;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 4px;
        transition: all 0.3s ease;
    }

    .mobile-view-btn:hover {
        background: var(--primary-color);
        color: white;
    }

    /* Hide desktop layout on mobile */
    .desktop-product-layout {
        display: none;
    }
}

/* Desktop product card layout */
@media (min-width: 768px) {
    .mobile-product-layout {
        display: none;
    }

    .desktop-product-layout {
        display: block;
    }
}

.product-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-heavy);
}

.product-card .badge {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    z-index: 3;
    font-size: 0.75rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-xl);
    font-weight: 700;
    letter-spacing: 0.5px;
    background: var(--secondary-color);
    color: white;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.product-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.product-price .original-price {
    text-decoration: line-through;
    color: #999;
    font-size: 0.9rem;
    margin-right: var(--spacing-sm);
    font-weight: 500;
}

/* Product card actions */
.product-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
    justify-content: space-between;
    margin-top: auto;
}

.product-actions .btn {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.85rem;
}

.product-actions .btn:first-child {
    flex: 2;
}

/* Quick add button */
.quick-add-btn {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    color: var(--primary-color);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 3;
}

.product-card:hover .quick-add-btn {
    opacity: 1;
    transform: scale(1);
}

.quick-add-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

/* Tablet product card adjustments */
@media (min-width: 768px) {
    .product-price {
        font-size: 1.2rem;
    }

    .product-actions .btn {
        font-size: 0.9rem;
    }
}

/* Desktop product card adjustments */
@media (min-width: 992px) {
    .product-card:hover {
        transform: translateY(-10px) scale(1.03);
    }

    .product-price {
        font-size: 1.3rem;
    }
}

/* Mobile-First Category Section */
.category-section {
    padding: 60px 0;
    background-color: var(--light-color);
}

/* Mobile-specific category design */
@media (max-width: 767px) {
    .category-section {
        padding: 40px 0;
        background: linear-gradient(180deg, white 0%, #f8f9ff 100%);
    }

    .mobile-category-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        padding: 0 5px;
    }

    .mobile-category-card {
        background: white;
        border-radius: 16px;
        padding: 20px 15px;
        text-align: center;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .mobile-category-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    }

    .mobile-category-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
    }

    .mobile-category-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 12px;
        color: white;
        font-size: 1.2rem;
    }

    .mobile-category-title {
        font-size: 0.9rem;
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 8px;
        line-height: 1.2;
    }

    .mobile-category-count {
        font-size: 0.75rem;
        color: #6c757d;
        margin-bottom: 12px;
    }

    .mobile-category-btn {
        background: var(--light-color);
        color: var(--primary-color);
        border: none;
        border-radius: 12px;
        padding: 6px 12px;
        font-size: 0.75rem;
        font-weight: 600;
        width: 100%;
        transition: all 0.3s ease;
    }

    .mobile-category-btn:hover {
        background: var(--primary-color);
        color: white;
    }

    /* Hide desktop layout on mobile */
    .desktop-category-layout {
        display: none;
    }
}

/* Desktop category layout */
@media (min-width: 768px) {
    .mobile-category-grid {
        display: none;
    }

    .desktop-category-layout {
        display: block;
    }

    .category-section {
        padding: 80px 0;
    }
}

.category-card {
    text-align: center;
    padding: 40px 20px;
    background: white;
    border-radius: 15px;
    transition: all 0.3s ease;
    height: 100%;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.category-card i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

/* Featured Products */
.featured-section {
    padding: 80px 0;
}

/* Mobile-First Section Titles */
.section-title {
    text-align: center;
    margin-bottom: 30px;
}

.section-title h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 10px;
}

.section-title p {
    font-size: 1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Mobile-specific section styling */
@media (max-width: 767px) {
    .section-title {
        margin-bottom: 25px;
        padding: 0 10px;
    }

    .section-title h2 {
        font-size: 1.6rem;
        margin-bottom: 8px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .section-title p {
        font-size: 0.9rem;
        opacity: 0.8;
    }
}

/* Desktop section styling */
@media (min-width: 768px) {
    .section-title {
        margin-bottom: 60px;
    }

    .section-title h2 {
        font-size: 2.5rem;
        margin-bottom: 15px;
    }

    .section-title p {
        font-size: 1.1rem;
    }
}

/* Cart Styles */
.cart-item {
    border-bottom: 1px solid var(--border-color);
    padding: 20px 0;
}

.cart-item:last-child {
    border-bottom: none;
}

.quantity-input {
    width: 80px;
    text-align: center;
}

/* Form Styles */
.form-control {
    border-radius: 10px;
    border: 2px solid var(--border-color);
    padding: 12px 15px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(108, 99, 255, 0.25);
}

/* Footer Styles */
footer {
    background: linear-gradient(135deg, #2c3e50, #34495e) !important;
}

footer .social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

footer .social-links a:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .hero-section p {
        font-size: 1rem;
    }
    
    .section-title h2 {
        font-size: 2rem;
    }
    
    .card-img-top {
        height: 200px;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

/* PWA-Style Mobile Enhancements */
/* Pull-to-refresh indicator */
.pull-to-refresh {
    position: fixed;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    font-size: 0.9rem;
    font-weight: 600;
    z-index: 1040;
    opacity: 0;
    transform: translateX(-50%) translateY(-100%);
    transition: all 0.3s ease;
}

.pull-to-refresh.show {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

/* Loading states */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Touch feedback */
.touch-feedback {
    position: relative;
    overflow: hidden;
}

.touch-feedback::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.touch-feedback:active::after {
    width: 200px;
    height: 200px;
}

/* Swipe gestures */
.swipeable {
    touch-action: pan-y;
    user-select: none;
}

/* Mobile-optimized forms */
.form-floating {
    position: relative;
}

.form-floating > .form-control {
    padding: var(--spacing-lg) var(--spacing-md) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    border: 2px solid var(--border-color);
    background: var(--light-color);
    transition: all 0.3s ease;
}

.form-floating > .form-control:focus {
    border-color: var(--primary-color);
    background: white;
    box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.1);
}

.form-floating > label {
    position: absolute;
    top: 0;
    left: var(--spacing-md);
    height: 100%;
    padding: var(--spacing-lg) 0 0;
    pointer-events: none;
    border: none;
    transform-origin: 0 0;
    transition: all 0.3s ease;
    color: var(--text-color);
    font-weight: 500;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    color: var(--primary-color);
}

/* Mobile-specific utilities */
.mobile-only {
    display: block;
}

.desktop-only {
    display: none;
}

@media (min-width: 992px) {
    .mobile-only {
        display: none;
    }

    .desktop-only {
        display: block;
    }
}

/* Safe area handling for notched devices */
@supports (padding: max(0px)) {
    .safe-area-top {
        padding-top: max(var(--spacing-md), env(safe-area-inset-top));
    }

    .safe-area-bottom {
        padding-bottom: max(var(--spacing-md), env(safe-area-inset-bottom));
    }

    .mobile-bottom-nav {
        padding-bottom: max(var(--spacing-sm), env(safe-area-inset-bottom));
    }
}

/* Improved scrolling */
.smooth-scroll {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Mobile-optimized shadows */
@media (max-width: 768px) {
    .card,
    .product-card,
    .category-card {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .card:hover,
    .product-card:hover,
    .category-card:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
}

/* Mobile-specific animations */
@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes swipeNotificationShow {
    from {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0;
    }
    to {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

@keyframes swipeNotificationHide {
    from {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    to {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Page transition animations */
.page-enter {
    animation: fadeInUp 0.6s ease-out;
}

.page-exit {
    animation: fadeOut 0.3s ease-in;
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* Mobile-first grid system enhancements */
.mobile-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
}

@media (min-width: 576px) {
    .mobile-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 768px) {
    .mobile-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 992px) {
    .mobile-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Enhanced mobile typography */
@media (max-width: 768px) {
    h1 { font-size: 1.8rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.3rem; }
    h4 { font-size: 1.1rem; }
    h5 { font-size: 1rem; }
    h6 { font-size: 0.9rem; }

    .display-1 { font-size: 3rem; }
    .display-2 { font-size: 2.5rem; }
    .display-3 { font-size: 2rem; }
    .display-4 { font-size: 1.8rem; }

    .lead {
        font-size: 1rem;
        font-weight: 400;
    }
}

/* Mobile-optimized spacing */
@media (max-width: 768px) {
    .py-5 { padding-top: 2rem !important; padding-bottom: 2rem !important; }
    .py-4 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !important; }
    .my-5 { margin-top: 2rem !important; margin-bottom: 2rem !important; }
    .my-4 { margin-top: 1.5rem !important; margin-bottom: 1.5rem !important; }

    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* Mobile Floating Action Button */
.mobile-fab {
    position: fixed;
    bottom: 100px;
    right: 20px;
    z-index: 1015;
}

.fab-button {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    color: white;
    font-size: 1.2rem;
    box-shadow: 0 4px 16px rgba(108, 99, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fab-button:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 24px rgba(108, 99, 255, 0.4);
}

.fab-button.active {
    transform: rotate(45deg);
}

.fab-menu {
    position: absolute;
    bottom: 70px;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fab-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.fab-item {
    display: flex;
    align-items: center;
    gap: 12px;
    background: white;
    color: var(--dark-color);
    text-decoration: none;
    padding: 12px 16px;
    border-radius: 28px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    white-space: nowrap;
    font-size: 0.9rem;
    font-weight: 500;
}

.fab-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(-8px);
    text-decoration: none;
}

.fab-item i {
    width: 20px;
    text-align: center;
}

/* Hide FAB on desktop */
@media (min-width: 992px) {
    .mobile-fab {
        display: none;
    }
}

/* Additional Modern Styling */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

/* Enhanced Product Cards */
.product-card {
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    border-radius: 20px;
    overflow: hidden;
    background: white;
}

.product-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.product-card .badge {
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 3;
    font-size: 0.75rem;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.product-card .card-img-top {
    transition: all 0.4s ease;
    border-radius: 0;
}

.product-card:hover .card-img-top {
    transform: scale(1.1);
}

/* Enhanced Buttons */
.btn {
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    border-radius: 25px;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #5a52d5);
    border: none;
    box-shadow: 0 4px 15px rgba(108, 99, 255, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a52d5, var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(108, 99, 255, 0.4);
}

/* Enhanced Navigation */
.navbar {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95) !important;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.8rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced Forms */
.form-control {
    border-radius: 15px;
    border: 2px solid #e9ecef;
    padding: 15px 20px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(108, 99, 255, 0.15);
    background: white;
    transform: translateY(-2px);
}

/* Enhanced Cards */
.card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background: white;
}

.card:hover {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: none;
    border-radius: 20px 20px 0 0 !important;
    padding: 20px 25px;
}

/* Enhanced Category Cards */
.category-card {
    background: white;
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.category-card:hover::before {
    opacity: 0.05;
}

.category-card:hover {
    transform: translateY(-15px) scale(1.05);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
}

.category-card i {
    font-size: 4rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 25px;
    transition: transform 0.3s ease;
}

.category-card:hover i {
    transform: scale(1.2) rotate(5deg);
}

/* Enhanced Footer */
footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%) !important;
    position: relative;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="footerPattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23footerPattern)"/></svg>');
}

footer .container {
    position: relative;
    z-index: 2;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Scroll animations */
.scroll-animate {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.scroll-animate.animate {
    opacity: 1;
    transform: translateY(0);
}
