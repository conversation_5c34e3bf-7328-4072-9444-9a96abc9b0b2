# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# Primo<PERSON> <<EMAIL>>, 2017
# <AUTHOR> <EMAIL>, 2013,2016
# <AUTHOR> <EMAIL>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-19 16:49+0100\n"
"PO-Revision-Date: 2017-09-23 18:54+0000\n"
"Last-Translator: Prim<PERSON><PERSON> Verdnik <<EMAIL>>\n"
"Language-Team: Slovenian (http://www.transifex.com/django/django/language/"
"sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n"
"%100==4 ? 2 : 3);\n"

msgid "Administrative Documentation"
msgstr "Dokumentacija o administraciji"

msgid "Home"
msgstr "Domov"

msgid "Documentation"
msgstr "Dokumentacija"

msgid "Bookmarklets"
msgstr "Apleti zaznamkov"

msgid "Documentation bookmarklets"
msgstr "Dokumentacijski apleti zaznamkov"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Aplete zaznamkov je mogoče namestiti z vleko povezave v orodno vrstico z "
"zaznamki, ali pa z desnim klikom na povezavo. Zaznamek je mogoče izbrati s "
"katerekoli strani tega spletnega mesta."

msgid "Documentation for this page"
msgstr "Dokumentacija te strani"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr "Skok na stran z dokumentacijo za pogled, ki gustvarja trenutno stran."

msgid "Tags"
msgstr "Oznake"

msgid "List of all the template tags and their functions."
msgstr "Izpiši vse značke za predloge in njihov opis."

msgid "Filters"
msgstr "Filtri"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filtri so opravila, ki jih lahko uporabite na spremenljivkah v predlogah, da "
"spremenite izpis."

msgid "Models"
msgstr "Modeli"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modeli so opisi vseh objektov v sistemu in njihovih pripadajočih polj. Vsak "
"model ima seznam polj, do katerih lahko dostopate v predlogi"

msgid "Views"
msgstr "Pogledi"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Vsaka stran na javni strani je zgenerirana iz pogleda. Pogled definira "
"katera predloga je uporabljena za generiranje strani in kateri objekti so "
"dostopni ti predlogi."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Orodja za vaš brskalnik, da lahko hitro dostopate do administracijskih "
"funkcij."

msgid "Please install docutils"
msgstr "Prosimo namestite paket docutils"

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Dokumentcijski sistem administracijskega vmesnika zahteva knjižnico <a href="
"\"%(link)s\">docutils</a> za Python."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Prosimo povprašajte administratorja, da namesti <a href=\"%(link)s"
"\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Polja"

msgid "Field"
msgstr "Polje"

msgid "Type"
msgstr "Tip"

msgid "Description"
msgstr "Opis"

msgid "Methods with arguments"
msgstr "Metode z argumenti"

msgid "Method"
msgstr "Metoda"

msgid "Arguments"
msgstr "Argumenti"

msgid "Back to Model documentation"
msgstr "Nazaj na dokumentacijo modelov"

msgid "Model documentation"
msgstr "Dokumentacija modelov"

msgid "Model groups"
msgstr "Skupine modelov"

msgid "Templates"
msgstr "Predloge"

#, python-format
msgid "Template: %(name)s"
msgstr "Predloga: %(name)s"

#, python-format
msgid "Template: \"%(name)s\""
msgstr "Predloga: \"%(name)s\""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template \"%(name)s\":"
msgstr "Poti iskanja za predlogo \"%(name)s\":"

msgid "(does not exist)"
msgstr "(ne obstaja)"

msgid "Back to Documentation"
msgstr "Nazaj na dokumentacijo"

msgid "Template filters"
msgstr "Filtri v predlogah"

msgid "Template filter documentation"
msgstr "Dokumentacija filtrov v predlogah"

msgid "Built-in filters"
msgstr "Vgrajeni filtri"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Če želite uporabiti te filtre, dodajte <code>%(code)s</code> v vašo predlogo "
"preden uporabite filter."

msgid "Template tags"
msgstr "Značke v predlogah"

msgid "Template tag documentation"
msgstr "Dokumentacija značk v predlogah"

msgid "Built-in tags"
msgstr "Vgrajene značke"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Če želite uporabiti te značke, dodajte <code>%(code)s</code> v vašo predlogo "
"preden uporabite značko."

#, python-format
msgid "View: %(name)s"
msgstr "Pogled: %(name)s"

msgid "Context:"
msgstr "Kontekst:"

msgid "Templates:"
msgstr "Predloge:"

msgid "Back to View documentation"
msgstr "Nazaj na dokumentacijo pogledov"

msgid "View documentation"
msgstr "Dokumentacija pogledov"

msgid "Jump to namespace"
msgstr "Skoči na imenski prostor"

msgid "Empty namespace"
msgstr "Prazen imenski prostor"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Pogledi v imenskem prostoru %(name)s"

msgid "Views by empty namespace"
msgstr "Pogledi brez imenskega prostora"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    Funkcija pogleda: <code>%(full_name)s</code>. Ime: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "oznaka:"

msgid "filter:"
msgstr "filter:"

msgid "view:"
msgstr "pogled:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Applikacije %(app_label)r ni bilo mogoče najti"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Modela %(model_name)r ni v programu %(app_label)r"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "povezani predmet `%(app_label)s.%(data_type)s`"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "povezani predmeti `%(app_label)s.%(object_name)s`"

#, python-format
msgid "all %s"
msgstr "vse %s"

#, python-format
msgid "number of %s"
msgstr "število %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "Predmet %s ni videti veljaven predmet urlpattern"
