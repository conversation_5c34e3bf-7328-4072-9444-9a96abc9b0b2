# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2011
# <AUTHOR> <EMAIL>, 2015-2017
# <AUTHOR> <EMAIL>, 2012
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2013
# <PERSON><PERSON> <<EMAIL>>, 2012-2013
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-24 13:46+0200\n"
"PO-Revision-Date: 2017-11-16 08:45+0000\n"
"Last-Translator: Mata<PERSON> <<EMAIL>>\n"
"Language-Team: Lithuanian (http://www.transifex.com/django/django/language/"
"lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < "
"11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? "
"1 : n % 1 != 0 ? 2: 3);\n"

msgid "Personal info"
msgstr "Asmeninė informacija"

msgid "Permissions"
msgstr "Leidimai"

msgid "Important dates"
msgstr "Svarbios datos"

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr "Įrašas %(name)s su pirminiu raktu %(key)r neegzistuoja."

msgid "Password changed successfully."
msgstr "Slaptažodis pakeistas sėkmingai."

#, python-format
msgid "Change password: %s"
msgstr "Pakeisti slaptažodį: %s"

msgid "Authentication and Authorization"
msgstr "Autentifikacija ir įgaliojimai"

msgid "password"
msgstr "slaptažodis"

msgid "last login"
msgstr "paskutinį kartą prisijungęs"

msgid "No password set."
msgstr "Slaptažodis nenustatytas"

msgid "Invalid password format or unknown hashing algorithm."
msgstr "Neteisingas slaptažodžio formatas arba nežinomas maišos algoritmas."

msgid "The two password fields didn't match."
msgstr "Slaptažodžio laukai nesutapo"

msgid "Password"
msgstr "Slaptažodis"

msgid "Password confirmation"
msgstr "Slaptažodžio patvirtinimas"

msgid "Enter the same password as before, for verification."
msgstr "Patikrinimui įveskite tokį patį slaptažodį, kaip anksčiau."

msgid ""
"Raw passwords are not stored, so there is no way to see this user's "
"password, but you can change the password using <a href=\"{}\">this form</a>."
msgstr ""
"Neužkoduoti slaptažodžiai nėra saugomi, todėl galimybės pasižiūrėti šio "
"vartotojo slaptažodį nėra, bet galite slaptažodį pakeisti naudodamiesi <a "
"href=\"{}\">šia forma</a>."

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""
"Įveskite teisingą %(username)s ir slaptažodį. Abiejuose laukuose didžiosios "
"mažosios raidės skiriasi."

msgid "This account is inactive."
msgstr "Ši paskyra yra neaktyvi."

msgid "Email"
msgstr "El. paštas"

msgid "New password"
msgstr "Naujas slaptažodis"

msgid "New password confirmation"
msgstr "Naujo slaptažodžio patvirtinimas"

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr "Blogai įvestas senas slaptažodis. Bandykite dar kartą."

msgid "Old password"
msgstr "Senas slaptažodis"

msgid "Password (again)"
msgstr "Slaptažodis (dar kartą)"

msgid "algorithm"
msgstr "algoritmas"

msgid "iterations"
msgstr "iteracijos"

msgid "salt"
msgstr "druska"

msgid "hash"
msgstr "maiša"

msgid "variety"
msgstr "įvairovė"

msgid "version"
msgstr "versija"

msgid "memory cost"
msgstr "atminties sąnaudos"

msgid "time cost"
msgstr "laiko sąnaudos"

msgid "parallelism"
msgstr "paralelizmas"

msgid "work factor"
msgstr "darbo faktorius"

msgid "checksum"
msgstr "kontrolinė suma"

msgid "name"
msgstr "vardas"

msgid "content type"
msgstr "turinio tipas"

msgid "codename"
msgstr "kodinis vardas"

msgid "permission"
msgstr "leidimas"

msgid "permissions"
msgstr "leidimai"

msgid "group"
msgstr "grupė"

msgid "groups"
msgstr "grupės"

msgid "superuser status"
msgstr "supervartotojo statusas"

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr "Pažymi, kad šis vartotojas turi visas teises be specialių nustatymų."

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""
"Grupės, kurioms šis vartotojas priklauso. Vartotojas gaus visas teises, "
"kurios yra suteiktos jo grupėms."

msgid "user permissions"
msgstr "vartotojo leidimai"

msgid "Specific permissions for this user."
msgstr "Specifiniai šio vartotojo leidimai."

msgid "username"
msgstr "vartotojo vardas"

msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr ""
"Privalomas. 150 arba mažiau simbolių. Raidės, skaičiai bei @/./+/-/_ "
"simboliai."

msgid "A user with that username already exists."
msgstr "Jau egzistuoja vartotojas su tokiu vardu."

msgid "first name"
msgstr "vardas"

msgid "last name"
msgstr "pavardė"

msgid "email address"
msgstr "El. pašto adresas"

msgid "staff status"
msgstr "personalo statusas"

msgid "Designates whether the user can log into this admin site."
msgstr "Nurodo ar vartotojas gali prisijungti prie administravimo puslapio."

msgid "active"
msgstr "aktyvus"

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""
"Nurodo ar vartotojas yra aktyvuotas. Užuot pašalinę vartotoją, galite nuimti "
"šią žymę."

msgid "date joined"
msgstr "data, kada prisijungė"

msgid "user"
msgstr "vartotojas"

msgid "users"
msgstr "vartotojai"

#, python-format
msgid ""
"This password is too short. It must contain at least %(min_length)d "
"character."
msgid_plural ""
"This password is too short. It must contain at least %(min_length)d "
"characters."
msgstr[0] ""
"Šis slaptažodis yra per trumpas. Jį turi sudaryti bent %(min_length)d "
"simbolis."
msgstr[1] ""
"Šis slaptažodis yra per trumpas. Jį turi sudaryti bent %(min_length)d "
"simboliai."
msgstr[2] ""
"Šis slaptažodis yra per trumpas. Jį turi sudaryti bent %(min_length)d "
"simbolių."
msgstr[3] ""
"Šis slaptažodis yra per trumpas. Jį turi sudaryti bent %(min_length)d "
"simbolių."

#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] "Jūsų slaptažodį turi sudaryti bent %(min_length)d simbolis."
msgstr[1] "Jūsų slaptažodį turi sudaryti bent %(min_length)d simboliai."
msgstr[2] "Jūsų slaptažodį turi sudaryti bent %(min_length)d simbolių."
msgstr[3] "Jūsų slaptažodį turi sudaryti bent %(min_length)d simbolių."

#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr "Slaptažodis pernelyg panašus į %(verbose_name)s."

msgid "Your password can't be too similar to your other personal information."
msgstr ""
"Jūsų slaptažodis negali būti pernelyg panašus į kitą jūsų asmeninę "
"informaciją."

msgid "This password is too common."
msgstr "Šis slaptažodis yra per dažnai pasitaikantis."

msgid "Your password can't be a commonly used password."
msgstr "Jūsų slaptažodis negali būti dažnai naudojamu slaptažodžiu."

msgid "This password is entirely numeric."
msgstr "Šis slaptažodis sudarytas tik iš skaitmenų."

msgid "Your password can't be entirely numeric."
msgstr "Jūsų slaptažodis negali būti sudarytas tik iš skaitmenų."

#, python-format
msgid "Password reset on %(site_name)s"
msgstr "Slaptažodis atkurtas ant %(site_name)s"

msgid ""
"Enter a valid username. This value may contain only English letters, "
"numbers, and @/./+/-/_ characters."
msgstr ""
"Įveskite korektišką vartotojo vardą. Ši reikšmė gali turėti tik angliško "
"alfabeto raides, skaičius  ir @/./+/-/_ simbolius."

msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""
"Įveskite korektišką vartotojo vardą. Ši reikšmė gali turėti tik raides, "
"skaičius  ir @/./+/-/_ simbolius."

msgid "Logged out"
msgstr "Atsijungęs"

msgid "Password reset"
msgstr "Slaptažodžio atstatymas"

msgid "Password reset sent"
msgstr "Slaptažodžio atstatymas išsiųstas"

msgid "Enter new password"
msgstr "Įveskite naują slaptažodį"

msgid "Password reset unsuccessful"
msgstr "Slaptažodžio atstatymas nesėkmingas"

msgid "Password reset complete"
msgstr "Slaptažodžio atstatymas baigtas"

msgid "Password change"
msgstr "Slaptažodžio keitimas"

msgid "Password change successful"
msgstr "Slaptažodis sėkmingai pakeistas"
