from django.contrib import admin
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.urls import reverse
from django.contrib.admin import AdminSite
from .models import Category, Product, Order, OrderItem, UserProfile

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'slug', 'product_count', 'image_preview', 'created_at']
    list_filter = ['created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    readonly_fields = ['created_at', 'updated_at', 'image_preview']

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" style="width: 50px; height: 50px; object-fit: cover; border-radius: 8px;" />',
                obj.image.url
            )
        return "No Image"
    image_preview.short_description = 'Preview'

    def product_count(self, obj):
        count = obj.products.count()
        return format_html(
            '<span style="background: #6c63ff; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">{}</span>',
            count
        )
    product_count.short_description = 'Products'

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ['image_preview', 'name', 'category', 'price_display', 'stock_status', 'is_featured', 'is_active', 'created_at']
    list_filter = ['category', 'is_featured', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    readonly_fields = ['created_at', 'updated_at', 'image_preview']
    list_editable = ['is_featured', 'is_active']
    list_per_page = 20

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'category', 'description')
        }),
        ('Pricing', {
            'fields': ('price', 'discount_price')
        }),
        ('Inventory', {
            'fields': ('stock', 'available_sizes')
        }),
        ('Media', {
            'fields': ('image',)
        }),
        ('Status', {
            'fields': ('is_featured', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" />',
                obj.image.url
            )
        return format_html('<div style="width: 60px; height: 60px; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #999;"><i class="fas fa-image"></i></div>')
    image_preview.short_description = 'Image'

    def price_display(self, obj):
        if obj.discount_price:
            return format_html(
                '<div style="display: flex; flex-direction: column;">'
                '<span style="color: #ff6b6b; font-weight: bold;">${}</span>'
                '<span style="color: #999; text-decoration: line-through; font-size: 12px;">${}</span>'
                '</div>',
                obj.discount_price, obj.price
            )
        return format_html('<span style="color: #6c63ff; font-weight: bold;">${}</span>', obj.price)
    price_display.short_description = 'Price'

    def stock_status(self, obj):
        if obj.stock > 10:
            color = '#51cf66'
            icon = 'fas fa-check-circle'
            text = f'{obj.stock} in stock'
        elif obj.stock > 0:
            color = '#ffd43b'
            icon = 'fas fa-exclamation-triangle'
            text = f'{obj.stock} low stock'
        else:
            color = '#ff6b6b'
            icon = 'fas fa-times-circle'
            text = 'Out of stock'

        return format_html(
            '<span style="color: {}; display: flex; align-items: center; gap: 5px;">'
            '<i class="{}"></i> {}</span>',
            color, icon, text
        )
    stock_status.short_description = 'Stock'

    def status_badges(self, obj):
        badges = []
        if obj.is_featured:
            badges.append('<span style="background: #6c63ff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px; margin-right: 4px;">Featured</span>')
        if obj.is_active:
            badges.append('<span style="background: #51cf66; color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px; margin-right: 4px;">Active</span>')
        else:
            badges.append('<span style="background: #ff6b6b; color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px; margin-right: 4px;">Inactive</span>')

        return format_html(''.join(badges))
    status_badges.short_description = 'Status'

class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 0
    readonly_fields = ['product', 'size', 'quantity', 'price', 'get_total_price']

    def get_total_price(self, obj):
        return f"${obj.get_total_price}"
    get_total_price.short_description = 'Total'

@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ['order_number', 'customer_info', 'status_badge', 'total_amount_display', 'items_count', 'created_at']
    list_filter = ['status', 'created_at']
    search_fields = ['order_number', 'user__username', 'email', 'first_name', 'last_name']
    readonly_fields = ['order_number', 'created_at', 'updated_at']
    inlines = [OrderItemInline]
    list_per_page = 25

    fieldsets = (
        ('Order Information', {
            'fields': ('order_number', 'user', 'status', 'total_amount')
        }),
        ('Customer Information', {
            'fields': ('first_name', 'last_name', 'email', 'phone')
        }),
        ('Shipping Address', {
            'fields': ('address', 'city', 'postal_code', 'country')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def customer_info(self, obj):
        return format_html(
            '<div style="display: flex; flex-direction: column;">'
            '<strong>{} {}</strong>'
            '<small style="color: #666;">{}</small>'
            '</div>',
            obj.first_name, obj.last_name, obj.email
        )
    customer_info.short_description = 'Customer'

    def status_badge(self, obj):
        status_colors = {
            'pending': '#ffd43b',
            'processing': '#74c0fc',
            'shipped': '#51cf66',
            'delivered': '#40c057',
            'cancelled': '#ff6b6b'
        }
        color = status_colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="background: {}; color: white; padding: 6px 12px; border-radius: 15px; font-size: 12px; font-weight: 500;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = 'Status'

    def total_amount_display(self, obj):
        return format_html(
            '<span style="color: #6c63ff; font-weight: bold; font-size: 14px;">${}</span>',
            obj.total_amount
        )
    total_amount_display.short_description = 'Total'

    def items_count(self, obj):
        count = obj.items.count()
        return format_html(
            '<span style="background: #e9ecef; color: #495057; padding: 4px 8px; border-radius: 12px; font-size: 12px;">{} items</span>',
            count
        )
    items_count.short_description = 'Items'

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'phone', 'city', 'country']
    search_fields = ['user__username', 'user__email', 'phone']
    list_filter = ['country', 'city']

    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Contact Information', {
            'fields': ('phone', 'address')
        }),
        ('Location', {
            'fields': ('city', 'postal_code', 'country')
        }),
    )

# Customize admin site
admin.site.site_header = "Fashion Store Admin"
admin.site.site_title = "Fashion Store Admin Portal"
admin.site.index_title = "Welcome to Fashion Store Administration"
