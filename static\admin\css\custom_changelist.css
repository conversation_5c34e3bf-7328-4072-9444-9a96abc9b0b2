/* Custom Change List Styles */

/* Results table styling */
#result_list {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: none;
}

#result_list thead th {
    background: linear-gradient(135deg, #6c63ff, #ff6b6b);
    color: white;
    font-weight: 600;
    padding: 15px 12px;
    border: none;
    font-size: 14px;
}

#result_list thead th a {
    color: white;
    text-decoration: none;
}

#result_list tbody tr {
    transition: all 0.3s ease;
}

#result_list tbody tr:hover {
    background: #f8f9ff;
    transform: scale(1.01);
}

#result_list tbody td {
    padding: 15px 12px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

#result_list tbody tr:last-child td {
    border-bottom: none;
}

/* Action buttons */
.actions {
    background: white;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.actions select {
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    margin-right: 10px;
}

.actions button {
    background: linear-gradient(135deg, #6c63ff, #ff6b6b);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.actions button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108, 99, 255, 0.3);
}

/* Filters */
#changelist-filter {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: none;
    padding: 20px;
}

#changelist-filter h2 {
    color: #6c63ff;
    font-weight: 600;
    margin-bottom: 15px;
}

#changelist-filter h3 {
    color: #495057;
    font-size: 14px;
    font-weight: 600;
    margin: 20px 0 10px 0;
}

#changelist-filter ul {
    list-style: none;
    padding: 0;
}

#changelist-filter li {
    margin-bottom: 8px;
}

#changelist-filter a {
    color: #6c757d;
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 6px;
    display: block;
    transition: all 0.3s ease;
}

#changelist-filter a:hover,
#changelist-filter a.selected {
    background: #6c63ff;
    color: white;
}

/* Search */
#changelist-search {
    background: white;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

#changelist-search input[type="text"] {
    width: 100%;
    padding: 12px 20px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 16px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

#changelist-search input[type="text"]:focus {
    outline: none;
    border-color: #6c63ff;
    background: white;
    box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.1);
}

#changelist-search input[type="submit"] {
    background: linear-gradient(135deg, #6c63ff, #ff6b6b);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 20px;
    margin-left: 10px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

#changelist-search input[type="submit"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108, 99, 255, 0.3);
}

/* Pagination */
.paginator {
    background: white;
    padding: 20px;
    border-radius: 15px;
    margin-top: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    text-align: center;
}

.paginator a,
.paginator .this-page {
    display: inline-block;
    padding: 10px 15px;
    margin: 0 5px;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.paginator a {
    color: #6c63ff;
    background: #f8f9ff;
}

.paginator a:hover {
    background: #6c63ff;
    color: white;
    transform: translateY(-2px);
}

.paginator .this-page {
    background: #6c63ff;
    color: white;
    font-weight: 600;
}

/* Add button */
.addlink {
    background: linear-gradient(135deg, #51cf66, #40c057) !important;
    color: white !important;
    padding: 12px 25px !important;
    border-radius: 25px !important;
    text-decoration: none !important;
    font-weight: 600 !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    transition: all 0.3s ease !important;
}

.addlink:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(81, 207, 102, 0.3) !important;
    color: white !important;
}

.addlink::before {
    content: '\f067';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
}

/* Object tools */
.object-tools {
    margin-bottom: 20px;
}

.object-tools li {
    margin-left: 10px;
}

.object-tools a {
    background: linear-gradient(135deg, #6c63ff, #ff6b6b) !important;
    color: white !important;
    padding: 10px 20px !important;
    border-radius: 20px !important;
    text-decoration: none !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.object-tools a:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(108, 99, 255, 0.3) !important;
}
