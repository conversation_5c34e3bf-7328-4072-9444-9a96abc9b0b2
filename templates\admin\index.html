{% extends "admin/base_site.html" %}
{% load i18n static admin_urls %}

{% block extrahead %}
{{ block.super }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="dashboard-header">
    <h1>
        <i class="fas fa-chart-line"></i>
        Dashboard Overview
    </h1>
    <p class="dashboard-subtitle">Welcome back, {{ user.get_full_name|default:user.username }}!</p>
</div>

<!-- Statistics Cards -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-shopping-bag"></i>
        </div>
        <div class="stat-content">
            <h3>{{ total_products|default:0 }}</h3>
            <p>Total Products</p>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-shopping-cart"></i>
        </div>
        <div class="stat-content">
            <h3>{{ total_orders|default:0 }}</h3>
            <p>Total Orders</p>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-users"></i>
        </div>
        <div class="stat-content">
            <h3>{{ total_users|default:0 }}</h3>
            <p>Total Users</p>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-dollar-sign"></i>
        </div>
        <div class="stat-content">
            <h3>${{ total_revenue|default:0 }}</h3>
            <p>Total Revenue</p>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="charts-section">
    <div class="chart-container">
        <h3><i class="fas fa-chart-bar"></i> Sales Overview</h3>
        <canvas id="salesChart"></canvas>
    </div>
    
    <div class="chart-container">
        <h3><i class="fas fa-chart-pie"></i> Category Distribution</h3>
        <canvas id="categoryChart"></canvas>
    </div>
</div>

<!-- Quick Actions -->
<div class="quick-actions">
    <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
    <div class="action-grid">
        <a href="{% url 'admin:store_product_add' %}" class="action-card">
            <i class="fas fa-plus"></i>
            <span>Add Product</span>
        </a>
        <a href="{% url 'admin:store_category_add' %}" class="action-card">
            <i class="fas fa-tags"></i>
            <span>Add Category</span>
        </a>
        <a href="{% url 'admin:store_order_changelist' %}" class="action-card">
            <i class="fas fa-list"></i>
            <span>View Orders</span>
        </a>
        <a href="{% url 'admin:auth_user_changelist' %}" class="action-card">
            <i class="fas fa-users-cog"></i>
            <span>Manage Users</span>
        </a>
    </div>
</div>

<!-- Recent Activity -->
<div class="recent-activity">
    <h3><i class="fas fa-clock"></i> Recent Activity</h3>
    <div class="activity-list">
        {% for order in recent_orders %}
        <div class="activity-item">
            <div class="activity-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="activity-content">
                <p><strong>New Order:</strong> #{{ order.order_number }}</p>
                <small>{{ order.created_at|timesince }} ago</small>
            </div>
            <div class="activity-amount">
                ${{ order.total_amount }}
            </div>
        </div>
        {% empty %}
        <div class="activity-item">
            <div class="activity-content">
                <p>No recent orders</p>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- App List -->
<div class="app-list-section">
    <h3><i class="fas fa-th-large"></i> Administration</h3>
    {% if app_list %}
        {% for app in app_list %}
            <div class="app-module">
                <h4>
                    <i class="fas fa-cube"></i>
                    {{ app.name }}
                </h4>
                <div class="model-list">
                    {% for model in app.models %}
                        <div class="model-item">
                            <a href="{{ model.admin_url }}" class="model-link">
                                <i class="fas fa-table"></i>
                                {{ model.name }}
                            </a>
                            {% if model.add_url %}
                                <a href="{{ model.add_url }}" class="add-link" title="Add {{ model.name }}">
                                    <i class="fas fa-plus"></i>
                                </a>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endfor %}
    {% else %}
        <p>No models available.</p>
    {% endif %}
</div>

<script>
// Sales Chart
const salesCtx = document.getElementById('salesChart').getContext('2d');
new Chart(salesCtx, {
    type: 'line',
    data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: 'Sales',
            data: [12, 19, 3, 5, 2, 3],
            borderColor: '#6c63ff',
            backgroundColor: 'rgba(108, 99, 255, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Category Chart
const categoryCtx = document.getElementById('categoryChart').getContext('2d');
new Chart(categoryCtx, {
    type: 'doughnut',
    data: {
        labels: ['Men\'s Clothing', 'Women\'s Clothing', 'Shoes', 'Accessories'],
        datasets: [{
            data: [30, 40, 20, 10],
            backgroundColor: ['#6c63ff', '#ff6b6b', '#4ecdc4', '#45b7d1']
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endblock %}
