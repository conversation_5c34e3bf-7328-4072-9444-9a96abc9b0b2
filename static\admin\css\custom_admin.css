/* Custom Admin Styles */
:root {
    --primary-color: #6c63ff;
    --secondary-color: #ff6b6b;
    --accent-color: #4ecdc4;
    --success-color: #51cf66;
    --warning-color: #ffd43b;
    --danger-color: #ff6b6b;
    --dark-color: #2c3e50;
    --light-color: #f8f9fa;
    --border-color: #e9ecef;
    --text-color: #495057;
    --sidebar-bg: #2c3e50;
    --sidebar-text: #ecf0f1;
}

/* Override default font */
body, input, textarea, select {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* Header Styling */
#header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    border-bottom: none !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1) !important;
}

.admin-branding h1 {
    color: white !important;
    font-weight: 700 !important;
    font-size: 1.5rem !important;
}

.admin-branding h1 a {
    color: white !important;
    text-decoration: none !important;
}

.admin-branding h1 i {
    margin-right: 10px;
    font-size: 1.3rem;
}

/* User tools styling */
#user-tools {
    color: white !important;
}

#user-tools a {
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: none !important;
    padding: 8px 12px !important;
    border-radius: 20px !important;
    transition: all 0.3s ease !important;
}

#user-tools a:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

/* Breadcrumbs */
.breadcrumbs {
    background: var(--light-color) !important;
    border: none !important;
    padding: 15px 20px !important;
    border-radius: 0 !important;
}

.breadcrumbs a {
    color: var(--primary-color) !important;
    text-decoration: none !important;
}

/* Dashboard Header */
.dashboard-header {
    background: white;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.dashboard-header h1 {
    color: var(--dark-color);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.dashboard-header h1 i {
    color: var(--primary-color);
    margin-right: 15px;
}

.dashboard-subtitle {
    color: var(--text-color);
    font-size: 1.1rem;
    margin: 0;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon { background: var(--primary-color); }
.stat-card:nth-child(2) .stat-icon { background: var(--secondary-color); }
.stat-card:nth-child(3) .stat-icon { background: var(--accent-color); }
.stat-card:nth-child(4) .stat-icon { background: var(--success-color); }

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin: 0 0 5px 0;
}

.stat-content p {
    color: var(--text-color);
    margin: 0;
    font-weight: 500;
}

/* Charts Section */
.charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.chart-container {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.chart-container h3 {
    color: var(--dark-color);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.chart-container h3 i {
    color: var(--primary-color);
    margin-right: 10px;
}

/* Quick Actions */
.quick-actions {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
}

.quick-actions h3 {
    color: var(--dark-color);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.quick-actions h3 i {
    color: var(--primary-color);
    margin-right: 10px;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.action-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: var(--light-color);
    border-radius: 10px;
    text-decoration: none;
    color: var(--dark-color);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.action-card:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    text-decoration: none;
}

.action-card i {
    font-size: 1.2rem;
    margin-right: 12px;
}

/* Recent Activity */
.recent-activity {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
}

.recent-activity h3 {
    color: var(--dark-color);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.recent-activity h3 i {
    color: var(--primary-color);
    margin-right: 10px;
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 15px;
}

.activity-content {
    flex: 1;
}

.activity-content p {
    margin: 0 0 5px 0;
    color: var(--dark-color);
}

.activity-content small {
    color: var(--text-color);
}

.activity-amount {
    font-weight: 700;
    color: var(--success-color);
}

/* App List Section */
.app-list-section {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.app-list-section h3 {
    color: var(--dark-color);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.app-list-section h3 i {
    color: var(--primary-color);
    margin-right: 10px;
}

.app-module {
    margin-bottom: 25px;
    padding: 20px;
    background: var(--light-color);
    border-radius: 10px;
}

.app-module h4 {
    color: var(--dark-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.app-module h4 i {
    color: var(--primary-color);
    margin-right: 8px;
}

.model-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.model-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 15px;
    background: white;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.model-item:hover {
    background: var(--primary-color);
}

.model-link {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 500;
    flex: 1;
}

.model-item:hover .model-link {
    color: white;
}

.model-link i {
    margin-right: 8px;
    color: var(--primary-color);
}

.model-item:hover .model-link i {
    color: white;
}

.add-link {
    color: var(--primary-color);
    text-decoration: none;
    padding: 5px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.model-item:hover .add-link {
    color: white;
    background: rgba(255, 255, 255, 0.2);
}

/* Footer */
#footer {
    background: var(--dark-color) !important;
    color: white !important;
    padding: 20px 0 !important;
    margin-top: 50px !important;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-left p, .footer-right p {
    margin: 0 !important;
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .action-grid {
        grid-template-columns: 1fr;
    }
    
    .model-list {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 10px;
    }
}
