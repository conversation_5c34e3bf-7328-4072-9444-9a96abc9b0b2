/* Custom Admin Styles */
:root {
    --primary-color: #6c63ff;
    --secondary-color: #ff6b6b;
    --accent-color: #4ecdc4;
    --success-color: #51cf66;
    --warning-color: #ffd43b;
    --danger-color: #ff6b6b;
    --dark-color: #2c3e50;
    --light-color: #f8f9fa;
    --border-color: #e9ecef;
    --text-color: #495057;
    --sidebar-bg: #2c3e50;
    --sidebar-text: #ecf0f1;
}

/* Override default font */
body, input, textarea, select {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* Header Styling */
#header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    border-bottom: none !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1) !important;
}

.admin-branding h1 {
    color: white !important;
    font-weight: 700 !important;
    font-size: 1.5rem !important;
}

.admin-branding h1 a {
    color: white !important;
    text-decoration: none !important;
}

.admin-branding h1 i {
    margin-right: 10px;
    font-size: 1.3rem;
}

/* User tools styling */
#user-tools {
    color: white !important;
}

#user-tools a {
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: none !important;
    padding: 8px 12px !important;
    border-radius: 20px !important;
    transition: all 0.3s ease !important;
}

#user-tools a:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

/* Breadcrumbs */
.breadcrumbs {
    background: var(--light-color) !important;
    border: none !important;
    padding: 15px 20px !important;
    border-radius: 0 !important;
}

.breadcrumbs a {
    color: var(--primary-color) !important;
    text-decoration: none !important;
}

/* Dashboard Header */
.dashboard-header {
    background: white;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.dashboard-header h1 {
    color: var(--dark-color);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.dashboard-header h1 i {
    color: var(--primary-color);
    margin-right: 15px;
}

.dashboard-subtitle {
    color: var(--text-color);
    font-size: 1.1rem;
    margin: 0;
}

/* Mobile-First Statistics Grid */
.stats-container {
    margin-bottom: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.products-card .stat-icon { background: linear-gradient(135deg, #6c63ff, #8b7fff); }
.orders-card .stat-icon { background: linear-gradient(135deg, #ff6b6b, #ff8e8e); }
.users-card .stat-icon { background: linear-gradient(135deg, #4ecdc4, #6dd5ed); }
.revenue-card .stat-icon { background: linear-gradient(135deg, #51cf66, #69db7c); }

.stat-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8rem;
    color: #51cf66;
    font-weight: 600;
}

.stat-trend i {
    font-size: 0.7rem;
}

.stat-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-color);
    margin: 0 0 5px 0;
    line-height: 1;
}

.stat-content p {
    color: #6c757d;
    margin: 0 0 12px 0;
    font-weight: 500;
    font-size: 0.9rem;
}

.stat-progress {
    height: 4px;
    background: #f1f3f4;
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
    transition: width 0.8s ease;
}

/* Tablet stats layout */
@media (min-width: 576px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
}

/* Desktop stats layout */
@media (min-width: 992px) {
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 24px;
    }

    .stat-card {
        padding: 24px;
    }

    .stat-content h3 {
        font-size: 2rem;
    }
}

/* Mobile-First Charts Section */
.charts-section {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-container {
    background: white;
    padding: 20px;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.chart-container:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.chart-header h3 {
    color: var(--dark-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.chart-header h3 i {
    color: var(--primary-color);
    font-size: 1rem;
}

.chart-controls select {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 6px 12px;
    font-size: 0.85rem;
    background: white;
}

.chart-wrapper {
    position: relative;
    height: 250px;
}

.chart-wrapper canvas {
    max-height: 100%;
}

/* Tablet charts layout */
@media (min-width: 768px) {
    .chart-container {
        padding: 24px;
    }

    .chart-header h3 {
        font-size: 1.2rem;
    }

    .chart-wrapper {
        height: 280px;
    }
}

/* Desktop charts layout */
@media (min-width: 992px) {
    .charts-section {
        grid-template-columns: 2fr 1fr;
        gap: 30px;
    }

    .chart-container {
        padding: 28px;
    }

    .chart-header h3 {
        font-size: 1.3rem;
    }

    .main-chart .chart-wrapper {
        height: 320px;
    }

    .side-chart .chart-wrapper {
        height: 280px;
    }
}

/* Quick Actions */
.quick-actions {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
}

.quick-actions h3 {
    color: var(--dark-color);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.quick-actions h3 i {
    color: var(--primary-color);
    margin-right: 10px;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.action-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: var(--light-color);
    border-radius: 10px;
    text-decoration: none;
    color: var(--dark-color);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.action-card:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    text-decoration: none;
}

.action-card i {
    font-size: 1.2rem;
    margin-right: 12px;
}

/* Recent Activity */
.recent-activity {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
}

.recent-activity h3 {
    color: var(--dark-color);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.recent-activity h3 i {
    color: var(--primary-color);
    margin-right: 10px;
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 15px;
}

.activity-content {
    flex: 1;
}

.activity-content p {
    margin: 0 0 5px 0;
    color: var(--dark-color);
}

.activity-content small {
    color: var(--text-color);
}

.activity-amount {
    font-weight: 700;
    color: var(--success-color);
}

/* App List Section */
.app-list-section {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.app-list-section h3 {
    color: var(--dark-color);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.app-list-section h3 i {
    color: var(--primary-color);
    margin-right: 10px;
}

.app-module {
    margin-bottom: 25px;
    padding: 20px;
    background: var(--light-color);
    border-radius: 10px;
}

.app-module h4 {
    color: var(--dark-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.app-module h4 i {
    color: var(--primary-color);
    margin-right: 8px;
}

.model-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.model-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 15px;
    background: white;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.model-item:hover {
    background: var(--primary-color);
}

.model-link {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 500;
    flex: 1;
}

.model-item:hover .model-link {
    color: white;
}

.model-link i {
    margin-right: 8px;
    color: var(--primary-color);
}

.model-item:hover .model-link i {
    color: white;
}

.add-link {
    color: var(--primary-color);
    text-decoration: none;
    padding: 5px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.model-item:hover .add-link {
    color: white;
    background: rgba(255, 255, 255, 0.2);
}

/* Footer */
#footer {
    background: var(--dark-color) !important;
    color: white !important;
    padding: 20px 0 !important;
    margin-top: 50px !important;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-left p, .footer-right p {
    margin: 0 !important;
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Mobile-First Responsive Design */
@media (max-width: 480px) {
    .dashboard-header {
        padding: 20px 15px;
        text-align: center;
    }

    .dashboard-header h1 {
        font-size: 1.8rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .stat-card {
        padding: 20px 15px;
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .charts-section {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .chart-container {
        padding: 20px 15px;
    }

    .quick-actions,
    .recent-activity,
    .app-list-section {
        padding: 20px 15px;
    }

    .action-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .action-card {
        padding: 15px;
        justify-content: center;
    }

    .model-list {
        grid-template-columns: 1fr;
    }

    .activity-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .activity-amount {
        align-self: flex-end;
    }
}

@media (max-width: 768px) {
    .charts-section {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .action-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .model-list {
        grid-template-columns: 1fr;
    }

    .footer-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    /* Mobile admin navigation */
    #header {
        padding: 10px 0;
    }

    .admin-branding h1 {
        font-size: 1.2rem;
    }

    #user-tools {
        font-size: 0.9rem;
    }

    #user-tools a {
        padding: 6px 10px;
        font-size: 0.8rem;
    }
}

/* Touch-friendly improvements */
@media (max-width: 768px) {
    .btn, .action-card, .model-item {
        min-height: 44px;
        padding: 12px 16px;
    }

    .stat-card:hover,
    .action-card:hover,
    .model-item:hover {
        transform: none; /* Disable hover transforms on mobile */
    }

    /* Larger touch targets */
    .activity-item {
        padding: 20px 0;
    }

    .model-link {
        padding: 12px 0;
    }
}
