# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2012
# <PERSON><PERSON> <<EMAIL>>, 2016
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-19 16:49+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Croatian (http://www.transifex.com/django/django/language/"
"hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

msgid "Administrative Documentation"
msgstr "Administrativna dokumentacija"

msgid "Home"
msgstr "Početna"

msgid "Documentation"
msgstr "Dokumentacija"

msgid "Bookmarklets"
msgstr "Bookmarklets"

msgid "Documentation bookmarklets"
msgstr "Dokumentacija bookmarklets-a"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""

msgid "Documentation for this page"
msgstr "Dokumentacija za ovu stranicu"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Preusmjeri te sa bilo koje stranice na dokumentaciju za taj prikaz (view) "
"koji generira stranicu."

msgid "Tags"
msgstr "Tagovi"

msgid "List of all the template tags and their functions."
msgstr ""

msgid "Filters"
msgstr "Filteri"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""

msgid "Models"
msgstr "Modeli"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""

msgid "Views"
msgstr "Views"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""

msgid "Please install docutils"
msgstr "Molimo instalirajte docutils"

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""

#, python-format
msgid "Model: %(name)s"
msgstr ""

msgid "Fields"
msgstr "Polja"

msgid "Field"
msgstr "Polje"

msgid "Type"
msgstr ""

msgid "Description"
msgstr "Opis"

msgid "Methods with arguments"
msgstr ""

msgid "Method"
msgstr ""

msgid "Arguments"
msgstr ""

msgid "Back to Model documentation"
msgstr ""

msgid "Model documentation"
msgstr ""

msgid "Model groups"
msgstr ""

msgid "Templates"
msgstr "Predlošci"

#, python-format
msgid "Template: %(name)s"
msgstr "Predložak: %(name)s"

#, python-format
msgid "Template: \"%(name)s\""
msgstr "Predložak: \"%(name)s\""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template \"%(name)s\":"
msgstr ""

msgid "(does not exist)"
msgstr "(ne postoji)"

msgid "Back to Documentation"
msgstr "Povratak na dokumentaciju"

msgid "Template filters"
msgstr ""

msgid "Template filter documentation"
msgstr ""

msgid "Built-in filters"
msgstr ""

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""

msgid "Template tags"
msgstr ""

msgid "Template tag documentation"
msgstr ""

msgid "Built-in tags"
msgstr ""

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""

#, python-format
msgid "View: %(name)s"
msgstr ""

msgid "Context:"
msgstr ""

msgid "Templates:"
msgstr "Predlošci:"

msgid "Back to View documentation"
msgstr ""

msgid "View documentation"
msgstr ""

msgid "Jump to namespace"
msgstr ""

msgid "Empty namespace"
msgstr ""

#, python-format
msgid "Views by namespace %(name)s"
msgstr ""

msgid "Views by empty namespace"
msgstr ""

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""

msgid "tag:"
msgstr "tag:"

msgid "filter:"
msgstr "filter:"

msgid "view:"
msgstr "prikaz:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Aplikacija %(app_label)r nije pronađena"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Model %(model_name)r nije pronađen u aplikaciji %(app_label)r"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "povezani `%(app_label)s.%(data_type)s` objekt"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "povezani `%(app_label)s.%(object_name)s` objekti"

#, python-format
msgid "all %s"
msgstr "svi %s"

#, python-format
msgid "number of %s"
msgstr "broj %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "izgleda da %s nije urlpattern objekt"
