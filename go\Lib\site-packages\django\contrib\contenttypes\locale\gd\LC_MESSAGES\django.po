# This file is distributed under the same license as the Django package.
#
# Translators:
# GunChleoc, 2015
# GunChleoc, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-12-13 12:51+0000\n"
"Last-Translator: GunChleoc\n"
"Language-Team: Gaelic, Scottish (http://www.transifex.com/django/django/"
"language/gd/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: gd\n"
"Plural-Forms: nplurals=4; plural=(n==1 || n==11) ? 0 : (n==2 || n==12) ? 1 : "
"(n > 2 && n < 20) ? 2 : 3;\n"

msgid "Content Types"
msgstr "Seòrsaichean susbainte"

msgid "python model class name"
msgstr "ainm clas air modail python"

msgid "content type"
msgstr "seòrsa susbainte"

msgid "content types"
msgstr "seòrsaichean susbainte"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr ""
"Chan eil modail co-cheangailte ris an oibseact le seòrsa susbaint %(ct_id)s"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Chan eil an oibseact %(obj_id)s le seòrsa susbaint %(ct_id)s ann"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "Chan eil am modh get_absolute_url() aig na h-oibseactan %(ct_name)s"
