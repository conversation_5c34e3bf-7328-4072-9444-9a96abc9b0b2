from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth import login, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib.auth.forms import UserCreationForm
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from .models import Product, Category, Order, OrderItem, UserProfile
from .cart import Cart
import uuid

def home(request):
    """Home page view"""
    featured_products = Product.objects.filter(is_featured=True, is_active=True)[:8]
    categories = Category.objects.all()[:6]
    latest_products = Product.objects.filter(is_active=True).order_by('-created_at')[:8]

    context = {
        'featured_products': featured_products,
        'categories': categories,
        'latest_products': latest_products,
    }
    return render(request, 'store/home.html', context)

def product_list(request):
    """Product listing with filtering and search"""
    products = Product.objects.filter(is_active=True)
    categories = Category.objects.all()

    # Search functionality
    query = request.GET.get('q')
    if query:
        products = products.filter(
            Q(name__icontains=query) |
            Q(description__icontains=query) |
            Q(category__name__icontains=query)
        )

    # Category filtering
    category_slug = request.GET.get('category')
    if category_slug:
        category = get_object_or_404(Category, slug=category_slug)
        products = products.filter(category=category)

    # Sorting
    sort_by = request.GET.get('sort', 'name')
    if sort_by == 'price_low':
        products = products.order_by('price')
    elif sort_by == 'price_high':
        products = products.order_by('-price')
    elif sort_by == 'newest':
        products = products.order_by('-created_at')
    else:
        products = products.order_by('name')

    # Pagination
    paginator = Paginator(products, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'categories': categories,
        'query': query,
        'current_category': category_slug,
        'current_sort': sort_by,
    }
    return render(request, 'store/product_list.html', context)

def product_detail(request, slug):
    """Product detail view"""
    product = get_object_or_404(Product, slug=slug, is_active=True)
    related_products = Product.objects.filter(
        category=product.category,
        is_active=True
    ).exclude(id=product.id)[:4]

    context = {
        'product': product,
        'related_products': related_products,
    }
    return render(request, 'store/product_detail.html', context)

def category_detail(request, slug):
    """Category detail view"""
    category = get_object_or_404(Category, slug=slug)
    products = Product.objects.filter(category=category, is_active=True)

    # Sorting
    sort_by = request.GET.get('sort', 'name')
    if sort_by == 'price_low':
        products = products.order_by('price')
    elif sort_by == 'price_high':
        products = products.order_by('-price')
    elif sort_by == 'newest':
        products = products.order_by('-created_at')
    else:
        products = products.order_by('name')

    # Pagination
    paginator = Paginator(products, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'category': category,
        'page_obj': page_obj,
        'current_sort': sort_by,
    }
    return render(request, 'store/category_detail.html', context)

# Cart Views
def cart_detail(request):
    """Cart detail view"""
    cart = Cart(request)
    return render(request, 'store/cart_detail.html', {'cart': cart})

@require_POST
def cart_add(request):
    """Add product to cart via AJAX"""
    cart = Cart(request)
    product_id = request.POST.get('product_id')
    size = request.POST.get('size', '')
    quantity = int(request.POST.get('quantity', 1))

    try:
        product = Product.objects.get(id=product_id, is_active=True)
        cart.add(product=product, quantity=quantity, size=size)

        return JsonResponse({
            'success': True,
            'cart_count': len(cart),
            'cart_total': str(cart.get_total_price())
        })
    except Product.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Product not found'})

@require_POST
def cart_remove(request):
    """Remove product from cart"""
    cart = Cart(request)
    product_id = request.POST.get('product_id')
    size = request.POST.get('size', '')

    try:
        product = Product.objects.get(id=product_id)
        cart.remove(product=product, size=size)

        return JsonResponse({
            'success': True,
            'cart_count': len(cart),
            'cart_total': str(cart.get_total_price())
        })
    except Product.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Product not found'})

@require_POST
def cart_update(request):
    """Update cart item quantity"""
    cart = Cart(request)
    product_id = request.POST.get('product_id')
    size = request.POST.get('size', '')
    quantity = int(request.POST.get('quantity', 1))

    try:
        product = Product.objects.get(id=product_id)
        cart.update_quantity(product=product, size=size, quantity=quantity)

        return JsonResponse({
            'success': True,
            'cart_count': len(cart),
            'cart_total': str(cart.get_total_price())
        })
    except Product.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Product not found'})

# Authentication Views
def register(request):
    """User registration view"""
    if request.method == 'POST':
        form = UserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            # Create user profile
            UserProfile.objects.create(user=user)
            username = form.cleaned_data.get('username')
            messages.success(request, f'Account created for {username}! You can now log in.')
            return redirect('login')
    else:
        form = UserCreationForm()
    return render(request, 'registration/register.html', {'form': form})

@login_required
def profile(request):
    """User profile view"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        # Update user info
        request.user.first_name = request.POST.get('first_name', '')
        request.user.last_name = request.POST.get('last_name', '')
        request.user.email = request.POST.get('email', '')
        request.user.save()

        # Update profile info
        profile.phone = request.POST.get('phone', '')
        profile.address = request.POST.get('address', '')
        profile.city = request.POST.get('city', '')
        profile.postal_code = request.POST.get('postal_code', '')
        profile.country = request.POST.get('country', '')
        profile.save()

        messages.success(request, 'Profile updated successfully!')
        return redirect('store:profile')

    context = {
        'profile': profile,
        'recent_orders': Order.objects.filter(user=request.user).order_by('-created_at')[:5]
    }
    return render(request, 'store/profile.html', context)

@login_required
def order_history(request):
    """User order history view"""
    orders = Order.objects.filter(user=request.user).order_by('-created_at')

    # Pagination
    paginator = Paginator(orders, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'store/order_history.html', {'page_obj': page_obj})

# Checkout and Order Views
@login_required
def checkout(request):
    """Checkout view"""
    cart = Cart(request)
    if len(cart) == 0:
        messages.warning(request, 'Your cart is empty.')
        return redirect('store:cart_detail')

    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        # Process order
        order_number = str(uuid.uuid4())[:8].upper()

        order = Order.objects.create(
            user=request.user,
            order_number=order_number,
            first_name=request.POST.get('first_name', ''),
            last_name=request.POST.get('last_name', ''),
            email=request.POST.get('email', ''),
            phone=request.POST.get('phone', ''),
            address=request.POST.get('address', ''),
            city=request.POST.get('city', ''),
            postal_code=request.POST.get('postal_code', ''),
            country=request.POST.get('country', ''),
            total_amount=cart.get_total_price()
        )

        # Create order items
        for item in cart:
            OrderItem.objects.create(
                order=order,
                product=item['product'],
                size=item['size'],
                quantity=item['quantity'],
                price=item['price']
            )

        # Clear cart
        cart.clear()

        messages.success(request, f'Order {order_number} placed successfully!')
        return redirect('store:order_detail', order_number=order_number)

    context = {
        'cart': cart,
        'profile': profile,
    }
    return render(request, 'store/checkout.html', context)

@login_required
def order_detail(request, order_number):
    """Order detail view"""
    order = get_object_or_404(Order, order_number=order_number, user=request.user)
    return render(request, 'store/order_detail.html', {'order': order})
