from django.core.management.base import BaseCommand
from django.utils.text import slugify
from store.models import Category, Product
from decimal import Decimal

class Command(BaseCommand):
    help = 'Populate the database with sample data'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample categories...')
        
        # Create categories
        categories_data = [
            {'name': 'Men\'s Clothing', 'description': 'Stylish clothing for men'},
            {'name': 'Women\'s Clothing', 'description': 'Fashionable clothing for women'},
            {'name': 'Shoes', 'description': 'Comfortable and stylish footwear'},
            {'name': 'Accessories', 'description': 'Fashion accessories and jewelry'},
            {'name': 'Bags', 'description': 'Handbags, backpacks, and more'},
            {'name': 'Sportswear', 'description': 'Athletic and casual wear'},
        ]
        
        categories = {}
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={
                    'slug': slugify(cat_data['name']),
                    'description': cat_data['description']
                }
            )
            categories[cat_data['name']] = category
            if created:
                self.stdout.write(f'Created category: {category.name}')

        self.stdout.write('Creating sample products...')
        
        # Create products
        products_data = [
            # Men's Clothing
            {'name': 'Classic White Shirt', 'category': 'Men\'s Clothing', 'price': 49.99, 'discount_price': 39.99, 'stock': 25, 'sizes': 'S,M,L,XL', 'featured': True},
            {'name': 'Denim Jeans', 'category': 'Men\'s Clothing', 'price': 79.99, 'stock': 30, 'sizes': '30,32,34,36,38', 'featured': True},
            {'name': 'Casual T-Shirt', 'category': 'Men\'s Clothing', 'price': 24.99, 'stock': 50, 'sizes': 'S,M,L,XL,XXL'},
            {'name': 'Formal Blazer', 'category': 'Men\'s Clothing', 'price': 149.99, 'discount_price': 119.99, 'stock': 15, 'sizes': 'S,M,L,XL'},
            
            # Women's Clothing
            {'name': 'Floral Summer Dress', 'category': 'Women\'s Clothing', 'price': 69.99, 'stock': 20, 'sizes': 'XS,S,M,L', 'featured': True},
            {'name': 'Elegant Blouse', 'category': 'Women\'s Clothing', 'price': 54.99, 'discount_price': 44.99, 'stock': 35, 'sizes': 'XS,S,M,L,XL'},
            {'name': 'High-Waist Jeans', 'category': 'Women\'s Clothing', 'price': 89.99, 'stock': 25, 'sizes': '26,28,30,32,34'},
            {'name': 'Cozy Sweater', 'category': 'Women\'s Clothing', 'price': 64.99, 'stock': 40, 'sizes': 'S,M,L,XL', 'featured': True},
            
            # Shoes
            {'name': 'Running Sneakers', 'category': 'Shoes', 'price': 99.99, 'discount_price': 79.99, 'stock': 30, 'sizes': '7,8,9,10,11,12'},
            {'name': 'Leather Boots', 'category': 'Shoes', 'price': 129.99, 'stock': 20, 'sizes': '7,8,9,10,11,12', 'featured': True},
            {'name': 'Canvas Sneakers', 'category': 'Shoes', 'price': 59.99, 'stock': 45, 'sizes': '6,7,8,9,10,11'},
            
            # Accessories
            {'name': 'Leather Watch', 'category': 'Accessories', 'price': 199.99, 'discount_price': 149.99, 'stock': 15, 'featured': True},
            {'name': 'Sunglasses', 'category': 'Accessories', 'price': 79.99, 'stock': 25},
            {'name': 'Silk Scarf', 'category': 'Accessories', 'price': 39.99, 'stock': 30},
            
            # Bags
            {'name': 'Leather Handbag', 'category': 'Bags', 'price': 159.99, 'discount_price': 129.99, 'stock': 18, 'featured': True},
            {'name': 'Travel Backpack', 'category': 'Bags', 'price': 89.99, 'stock': 22},
            {'name': 'Crossbody Bag', 'category': 'Bags', 'price': 49.99, 'stock': 35},
            
            # Sportswear
            {'name': 'Yoga Leggings', 'category': 'Sportswear', 'price': 44.99, 'stock': 40, 'sizes': 'XS,S,M,L,XL'},
            {'name': 'Athletic Shorts', 'category': 'Sportswear', 'price': 29.99, 'stock': 50, 'sizes': 'S,M,L,XL'},
            {'name': 'Sports Bra', 'category': 'Sportswear', 'price': 34.99, 'stock': 30, 'sizes': 'XS,S,M,L,XL'},
        ]
        
        for product_data in products_data:
            category = categories[product_data['category']]
            
            product, created = Product.objects.get_or_create(
                name=product_data['name'],
                defaults={
                    'slug': slugify(product_data['name']),
                    'category': category,
                    'description': f"High-quality {product_data['name'].lower()} perfect for any occasion. Made with premium materials for comfort and durability.",
                    'price': Decimal(str(product_data['price'])),
                    'discount_price': Decimal(str(product_data.get('discount_price', 0))) if product_data.get('discount_price') else None,
                    'stock': product_data['stock'],
                    'available_sizes': product_data.get('sizes', ''),
                    'is_featured': product_data.get('featured', False),
                    'is_active': True,
                }
            )
            
            if created:
                self.stdout.write(f'Created product: {product.name}')

        self.stdout.write(self.style.SUCCESS('Successfully populated database with sample data!'))
