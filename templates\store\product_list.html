{% extends 'base.html' %}
{% load static %}

{% block title %}Products - Fashion Store{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Filters</h5>
                </div>
                <div class="card-body">
                    <!-- Search -->
                    <form method="get" class="mb-4">
                        <div class="input-group">
                            <input type="text" class="form-control" name="q" value="{{ query }}" placeholder="Search products...">
                            <button class="btn btn-outline-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                    
                    <!-- Categories -->
                    <h6 class="fw-bold mb-3">Categories</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <a href="{% url 'store:product_list' %}" class="text-decoration-none {% if not current_category %}text-primary fw-bold{% endif %}">
                                All Products
                            </a>
                        </li>
                        {% for category in categories %}
                        <li class="mb-2">
                            <a href="?category={{ category.slug }}" class="text-decoration-none {% if current_category == category.slug %}text-primary fw-bold{% endif %}">
                                {{ category.name }}
                            </a>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Products -->
        <div class="col-lg-9">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>Products</h2>
                    <p class="text-muted mb-0">{{ page_obj.paginator.count }} products found</p>
                </div>
                
                <!-- Sort Options -->
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        Sort by
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item {% if current_sort == 'name' %}active{% endif %}" href="?sort=name{% if query %}&q={{ query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}">Name</a></li>
                        <li><a class="dropdown-item {% if current_sort == 'price_low' %}active{% endif %}" href="?sort=price_low{% if query %}&q={{ query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}">Price: Low to High</a></li>
                        <li><a class="dropdown-item {% if current_sort == 'price_high' %}active{% endif %}" href="?sort=price_high{% if query %}&q={{ query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}">Price: High to Low</a></li>
                        <li><a class="dropdown-item {% if current_sort == 'newest' %}active{% endif %}" href="?sort=newest{% if query %}&q={{ query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}">Newest</a></li>
                    </ul>
                </div>
            </div>
            
            <!-- Mobile Product List -->
            <div class="d-md-none">
                {% for product in page_obj %}
                <div class="product-card mb-3 swipeable">
                    <div class="mobile-product-layout">
                        <div class="mobile-product-image">
                            {% if product.image %}
                            <img src="{{ product.image.url }}" alt="{{ product.name }}">
                            {% else %}
                            <i class="fas fa-image text-muted"></i>
                            {% endif %}
                        </div>
                        <div class="mobile-product-content">
                            <div class="mobile-product-title">{{ product.name }}</div>
                            <div class="mobile-product-category">{{ product.category.name }}</div>
                            <div class="mobile-product-price">
                                {% if product.discount_price %}
                                <span class="original-price">${{ product.price }}</span>
                                {% endif %}
                                ${{ product.get_price }}
                                {% if product.get_discount_percentage %}
                                <span class="badge bg-danger ms-2">{{ product.get_discount_percentage }}% OFF</span>
                                {% endif %}
                            </div>
                            <div class="mobile-product-actions">
                                <a href="{{ product.get_absolute_url }}" class="mobile-view-btn">
                                    <i class="fas fa-eye"></i>View
                                </a>
                                <button class="mobile-add-btn add-to-cart" data-product-id="{{ product.id }}">
                                    <i class="fas fa-plus"></i>Add
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Desktop Product Grid -->
            <div class="mobile-grid d-none d-md-grid">
                {% for product in page_obj %}
                <div class="card product-card h-100 swipeable desktop-product-layout">
                    {% if product.get_discount_percentage %}
                    <span class="badge">{{ product.get_discount_percentage }}% OFF</span>
                    {% endif %}
                    <button class="quick-add-btn add-to-cart" data-product-id="{{ product.id }}" title="Quick Add">
                        <i class="fas fa-plus"></i>
                    </button>
                    {% if product.image %}
                    <img src="{{ product.image.url }}" class="card-img-top" alt="{{ product.name }}">
                    {% else %}
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                        <i class="fas fa-image text-muted fa-2x"></i>
                    </div>
                    {% endif %}
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title">{{ product.name }}</h6>
                        <p class="card-text text-muted small flex-grow-1">{{ product.description|truncatewords:12 }}</p>
                        <div class="product-price mb-3">
                            {% if product.discount_price %}
                            <span class="original-price">${{ product.price }}</span>
                            {% endif %}
                            ${{ product.get_price }}
                        </div>
                        <div class="product-actions mt-auto">
                            <a href="{{ product.get_absolute_url }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>View
                            </a>
                            <button class="btn btn-primary btn-sm add-to-cart" data-product-id="{{ product.id }}">
                                <i class="fas fa-cart-plus me-1"></i>Add
                            </button>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-5" style="grid-column: 1 / -1;">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4>No products found</h4>
                    <p class="text-muted">Try adjusting your search or filter criteria.</p>
                    <a href="{% url 'store:product_list' %}" class="btn btn-primary">View All Products</a>
                </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Product pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if query %}&q={{ query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_sort %}&sort={{ current_sort }}{% endif %}">Previous</a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if query %}&q={{ query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_sort %}&sort={{ current_sort }}{% endif %}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if query %}&q={{ query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_sort %}&sort={{ current_sort }}{% endif %}">Next</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add CSRF token for AJAX requests
    const csrfToken = '{{ csrf_token }}';
</script>
{% endblock %}
