# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
# yubike, 2024
# <PERSON><PERSON> <<EMAIL>>, 2011
# <AUTHOR> <EMAIL>, 2013
# 619e61dbdb61c57213f62815aaf60e01, 2011
# Tzu-ping <PERSON> <<EMAIL>>, 2016,2019
# YAO WEN LIANG, 2024
# <PERSON><PERSON>-Yung <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2024-08-07 20:19+0000\n"
"Last-Translator: YAO WEN LIANG, 2024\n"
"Language-Team: Chinese (Taiwan) (http://app.transifex.com/django/django/"
"language/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Administrative Documentation"
msgstr "管理文件"

msgid "Home"
msgstr "首頁"

msgid "Documentation"
msgstr "文件"

msgid "Bookmarklets"
msgstr "書籤"

msgid "Documentation bookmarklets"
msgstr "文件書籤"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"要安裝書籤，把連結拖進你的書籤工具列，或右擊該連結後新增到你的書籤裡。現在你"
"可以從網站的任何頁面來選擇書籤。"

msgid "Documentation for this page"
msgstr "本頁面的文件"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr "從任何頁面跳到產生該頁面的視圖的文件。"

msgid "Tags"
msgstr "標籤"

msgid "List of all the template tags and their functions."
msgstr "列出所有模板標籤與其函數"

msgid "Filters"
msgstr "過濾器"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr "過瀘器可套用在模板內的變數，用來改變輸出。"

msgid "Models"
msgstr "模型"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"模型是對系統裡所有物件及相關欄位的描述。模型裡的欄位都可能以模板變數來取用。"

msgid "Views"
msgstr "視圖"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"網站上的每個頁面都是由視圖 \"view\" 來生成。每個視圖定義了生成頁面的模板，以"
"及模板裡有什麼物件可使用。"

msgid "Tools for your browser to quickly access admin functionality."
msgstr "讓您的瀏覽器快速存取管理功能的工具。"

msgid "Please install docutils"
msgstr "請安裝 docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a "
"href=\"%(link)s\">docutils</a> library."
msgstr "管理文件系統需要 Python 的 <a href=\"%(link)s\">docutils</a> 函式庫。"

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr "請要求您的管理員安裝 <a href=\"%(link)s\">docutils</a>。"

#, python-format
msgid "Model: %(name)s"
msgstr "模型：%(name)s"

msgid "Fields"
msgstr "欄位"

msgid "Field"
msgstr "欄位"

msgid "Type"
msgstr "類別"

msgid "Description"
msgstr "描述"

msgid "Methods with arguments"
msgstr "方法和參數"

msgid "Method"
msgstr "方法"

msgid "Arguments"
msgstr "參數"

msgid "Back to Model documentation"
msgstr "回到模型文件"

msgid "Model documentation"
msgstr "模型文件"

msgid "Model groups"
msgstr "模型群組"

msgid "Templates"
msgstr "模版"

#, python-format
msgid "Template: %(name)s"
msgstr "模版：%(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "模板：<q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "搜尋模板 <q>%(name)s</q> 的路徑："

msgid "(does not exist)"
msgstr "(不存在)"

msgid "Back to Documentation"
msgstr "回到文件"

msgid "Template filters"
msgstr "模板過濾器"

msgid "Template filter documentation"
msgstr "模板過濾器文件"

msgid "Built-in filters"
msgstr "內建過濾器"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"要使用這些過濾器, 在你使用過濾器之前需要在模板中放置 <code>%(code)s</code> 。"

msgid "Template tags"
msgstr "模版標籤"

msgid "Template tag documentation"
msgstr "模板標籤文件"

msgid "Built-in tags"
msgstr "內建標籤"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"要使用這些標籤, 在你使用標籤之前需要在模板中放置 <code>%(code)s</code> 。"

#, python-format
msgid "View: %(name)s"
msgstr "視圖：%(name)s"

msgid "Context:"
msgstr "內容:"

msgid "Templates:"
msgstr "模版："

msgid "Back to View documentation"
msgstr "回到視圖文件"

msgid "View documentation"
msgstr "視圖文件"

msgid "Jump to namespace"
msgstr "跳至名稱空間"

msgid "Empty namespace"
msgstr "空的名稱空間"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "按命名空間分類 %(name)s 的視圖"

msgid "Views by empty namespace"
msgstr "未指定命名空間的視圖"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"視圖函數：<code>%(full_name)s</code>，名稱：<code>%(url_name)s</code>\n"

msgid "tag:"
msgstr "標籤："

msgid "filter:"
msgstr "過濾器："

msgid "view:"
msgstr "視圖："

#, python-format
msgid "App %(app_label)r not found"
msgstr "找不到 App %(app_label)r"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "未在%(app_label)r 找到模組 %(model_name)r "

msgid "model:"
msgstr "模型："

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "有關 `%(app_label)s.%(data_type)s` 的物件"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "有關 `%(app_label)s.%(object_name)s` 的物件"

#, python-format
msgid "all %s"
msgstr "所有 %s"

#, python-format
msgid "number of %s"
msgstr "%s 的数量"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s 似乎不是一個 urlpattern 物件"
