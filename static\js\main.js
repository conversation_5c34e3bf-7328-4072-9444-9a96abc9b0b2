// Mobile-First JavaScript for Fashion Store

document.addEventListener('DOMContentLoaded', function() {
    // Initialize mobile-friendly features
    initMobileFeatures();
    initTouchFeedback();
    initSwipeGestures();
    initPWAFeatures();

    // Initialize tooltips (desktop only)
    if (window.innerWidth >= 992) {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // Cart functionality
    const cartButtons = document.querySelectorAll('.add-to-cart');
    cartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const productId = this.dataset.productId;
            const size = this.dataset.size || '';
            addToCart(productId, size);
        });
    });

    // Quantity update functionality
    const quantityInputs = document.querySelectorAll('.quantity-input');
    quantityInputs.forEach(input => {
        input.addEventListener('change', function() {
            const productId = this.dataset.productId;
            const size = this.dataset.size || '';
            const quantity = parseInt(this.value);
            updateCartQuantity(productId, size, quantity);
        });
    });

    // Search functionality
    const searchForm = document.querySelector('#search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            const searchInput = this.querySelector('input[name="q"]');
            if (!searchInput.value.trim()) {
                e.preventDefault();
                searchInput.focus();
            }
        });
    }
});

// Add to cart function
function addToCart(productId, size = '') {
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
    
    fetch('/cart/add/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': csrfToken
        },
        body: `product_id=${productId}&size=${size}&quantity=1`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update cart count in navbar
            updateCartCount(data.cart_count);
            showNotification('Product added to cart!', 'success');
        } else {
            showNotification('Error adding product to cart', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error adding product to cart', 'error');
    });
}

// Update cart quantity
function updateCartQuantity(productId, size, quantity) {
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
    
    fetch('/cart/update/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': csrfToken
        },
        body: `product_id=${productId}&size=${size}&quantity=${quantity}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update cart count and total
            updateCartCount(data.cart_count);
            updateCartTotal(data.cart_total);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// Update cart count in navbar
function updateCartCount(count) {
    const cartBadge = document.querySelector('.navbar .badge');
    if (cartBadge) {
        cartBadge.textContent = count;
        if (count > 0) {
            cartBadge.style.display = 'inline';
        } else {
            cartBadge.style.display = 'none';
        }
    }
}

// Update cart total
function updateCartTotal(total) {
    const cartTotal = document.querySelector('#cart-total');
    if (cartTotal) {
        cartTotal.textContent = `$${total}`;
    }
}

// Show notification
function showNotification(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 'alert-info';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.container');
    if (container) {
        container.insertAdjacentHTML('afterbegin', alertHtml);
        
        // Auto-dismiss after 3 seconds
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 3000);
    }
}

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Image lazy loading
if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
    });
}

// Navbar scroll effect
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 50) {
        navbar.classList.add('scrolled');
    } else {
        navbar.classList.remove('scrolled');
    }
});

// Scroll animations
if ('IntersectionObserver' in window) {
    const scrollObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    document.querySelectorAll('.scroll-animate').forEach(el => {
        scrollObserver.observe(el);
    });
}

// Enhanced product card interactions
document.querySelectorAll('.product-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-8px) scale(1.02)';
    });

    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
});

// Loading states for buttons
function addLoadingState(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<span class="loading"></span> Adding...';
    button.disabled = true;

    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 1000);
}

// Enhanced add to cart with loading state
document.querySelectorAll('.add-to-cart').forEach(button => {
    button.addEventListener('click', function() {
        addLoadingState(this);
    });
});

// Smooth page transitions
document.addEventListener('DOMContentLoaded', function() {
    document.body.classList.add('fade-in-up');
});

// Enhanced form validation
document.querySelectorAll('form').forEach(form => {
    form.addEventListener('submit', function(e) {
        const requiredFields = this.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            showNotification('Please fill in all required fields', 'error');
        }
    });
});

// Auto-hide alerts
document.querySelectorAll('.alert').forEach(alert => {
    setTimeout(() => {
        if (alert && alert.parentNode) {
            alert.style.opacity = '0';
            alert.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }
    }, 5000);
});

// Enhanced search functionality
const searchInput = document.querySelector('input[name="q"]');
if (searchInput) {
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();

        if (query.length > 2) {
            searchTimeout = setTimeout(() => {
                // You could implement live search here
                console.log('Searching for:', query);
            }, 300);
        }
    });
}

// Mobile-specific functions
function initMobileFeatures() {
    // Prevent zoom on input focus (iOS)
    if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
        const inputs = document.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                document.querySelector('meta[name=viewport]').setAttribute(
                    'content',
                    'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
                );
            });

            input.addEventListener('blur', function() {
                document.querySelector('meta[name=viewport]').setAttribute(
                    'content',
                    'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
                );
            });
        });
    }

    // Mobile navigation active states
    updateActiveNavigation();

    // Optimize images for mobile
    optimizeImagesForMobile();
}

function initTouchFeedback() {
    // Add touch feedback to interactive elements
    const touchElements = document.querySelectorAll('.btn, .card, .nav-link, .bottom-nav-item');

    touchElements.forEach(element => {
        element.classList.add('touch-feedback');

        element.addEventListener('touchstart', function(e) {
            this.style.transform = 'scale(0.98)';
        });

        element.addEventListener('touchend', function(e) {
            this.style.transform = 'scale(1)';
        });

        element.addEventListener('touchcancel', function(e) {
            this.style.transform = 'scale(1)';
        });
    });
}

function initSwipeGestures() {
    // Add swipe gestures for product cards
    const productCards = document.querySelectorAll('.product-card');

    productCards.forEach(card => {
        let startX, startY, distX, distY;

        card.addEventListener('touchstart', function(e) {
            const touch = e.touches[0];
            startX = touch.clientX;
            startY = touch.clientY;
        });

        card.addEventListener('touchmove', function(e) {
            if (!startX || !startY) return;

            const touch = e.touches[0];
            distX = touch.clientX - startX;
            distY = touch.clientY - startY;

            // Prevent default if horizontal swipe
            if (Math.abs(distX) > Math.abs(distY)) {
                e.preventDefault();
            }
        });

        card.addEventListener('touchend', function(e) {
            if (!startX || !startY) return;

            // Swipe right to add to cart
            if (distX > 100 && Math.abs(distY) < 100) {
                const addButton = this.querySelector('.add-to-cart');
                if (addButton) {
                    addButton.click();
                    showSwipeNotification('Added to cart!');
                }
            }

            // Reset
            startX = startY = distX = distY = null;
        });
    });
}

function initPWAFeatures() {
    // Add to home screen prompt
    let deferredPrompt;

    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;
        showInstallPrompt();
    });

    // Service worker registration
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => console.log('SW registered'))
            .catch(error => console.log('SW registration failed'));
    }
}

function updateActiveNavigation() {
    const currentPath = window.location.pathname;
    const navItems = document.querySelectorAll('.bottom-nav-item');

    navItems.forEach(item => {
        const href = item.getAttribute('href');
        if (href && currentPath.includes(href)) {
            item.classList.add('active');
        } else {
            item.classList.remove('active');
        }
    });
}

function optimizeImagesForMobile() {
    // Lazy load images on mobile
    if ('IntersectionObserver' in window && window.innerWidth <= 768) {
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    }
}

function showSwipeNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'swipe-notification';
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: var(--success-color);
        color: white;
        padding: 12px 24px;
        border-radius: 25px;
        font-weight: 600;
        z-index: 9999;
        animation: swipeNotificationShow 0.3s ease;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'swipeNotificationHide 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 2000);
}

function showInstallPrompt() {
    // Create install prompt
    const installBanner = document.createElement('div');
    installBanner.className = 'install-banner';
    installBanner.innerHTML = `
        <div class="install-content">
            <i class="fas fa-mobile-alt"></i>
            <span>Install Fashion Store app for better experience!</span>
            <button class="btn btn-primary btn-sm install-btn">Install</button>
            <button class="btn btn-outline-secondary btn-sm dismiss-btn">×</button>
        </div>
    `;

    installBanner.style.cssText = `
        position: fixed;
        bottom: 80px;
        left: 16px;
        right: 16px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        padding: 16px;
        z-index: 1025;
        animation: slideUp 0.3s ease;
    `;

    document.body.appendChild(installBanner);

    // Handle install
    installBanner.querySelector('.install-btn').addEventListener('click', () => {
        if (deferredPrompt) {
            deferredPrompt.prompt();
            deferredPrompt.userChoice.then(() => {
                deferredPrompt = null;
                installBanner.remove();
            });
        }
    });

    // Handle dismiss
    installBanner.querySelector('.dismiss-btn').addEventListener('click', () => {
        installBanner.remove();
    });
}
