{% extends 'base.html' %}
{% load static %}

{% block title %}Shopping Cart - Fashion Store{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">Shopping Cart</h2>
            
            {% if cart %}
            <div class="row">
                <!-- Cart Items -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-body">
                            {% for item in cart %}
                            <div class="cart-item row align-items-center">
                                <div class="col-md-2">
                                    {% if item.product.image %}
                                    <img src="{{ item.product.image.url }}" class="img-fluid rounded" alt="{{ item.product.name }}">
                                    {% else %}
                                    <div class="bg-light d-flex align-items-center justify-content-center rounded" style="height: 80px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4">
                                    <h6 class="mb-1">{{ item.product.name }}</h6>
                                    <p class="text-muted mb-1">{{ item.product.category.name }}</p>
                                    {% if item.size %}
                                    <small class="text-muted">Size: {{ item.size }}</small>
                                    {% endif %}
                                </div>
                                <div class="col-md-2">
                                    <div class="input-group">
                                        <button class="btn btn-outline-secondary btn-sm" type="button" onclick="updateQuantity('{{ item.product.id }}', '{{ item.size }}', {{ item.quantity|add:'-1' }})">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="number" class="form-control form-control-sm text-center quantity-input" 
                                               value="{{ item.quantity }}" min="1" 
                                               data-product-id="{{ item.product.id }}" 
                                               data-size="{{ item.size }}"
                                               onchange="updateQuantity('{{ item.product.id }}', '{{ item.size }}', this.value)">
                                        <button class="btn btn-outline-secondary btn-sm" type="button" onclick="updateQuantity('{{ item.product.id }}', '{{ item.size }}', {{ item.quantity|add:'1' }})">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-2 text-center">
                                    <span class="fw-bold">${{ item.price }}</span>
                                </div>
                                <div class="col-md-2 text-center">
                                    <span class="fw-bold">${{ item.total_price }}</span>
                                    <button class="btn btn-outline-danger btn-sm ms-2" onclick="removeFromCart('{{ item.product.id }}', '{{ item.size }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <!-- Continue Shopping -->
                    <div class="mt-3">
                        <a href="{% url 'store:product_list' %}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>Continue Shopping
                        </a>
                    </div>
                </div>
                
                <!-- Cart Summary -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Order Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Subtotal ({{ cart|length }} items):</span>
                                <span id="cart-total">${{ cart.get_total_price }}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Shipping:</span>
                                <span>Free</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Tax:</span>
                                <span>Calculated at checkout</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <strong>Total:</strong>
                                <strong>${{ cart.get_total_price }}</strong>
                            </div>
                            
                            {% if user.is_authenticated %}
                            <a href="{% url 'store:checkout' %}" class="btn btn-primary w-100 mb-2">
                                <i class="fas fa-credit-card me-2"></i>Proceed to Checkout
                            </a>
                            {% else %}
                            <a href="{% url 'login' %}?next={% url 'store:checkout' %}" class="btn btn-primary w-100 mb-2">
                                <i class="fas fa-sign-in-alt me-2"></i>Login to Checkout
                            </a>
                            {% endif %}
                            
                            <button class="btn btn-outline-secondary w-100" onclick="clearCart()">
                                <i class="fas fa-trash me-2"></i>Clear Cart
                            </button>
                        </div>
                    </div>
                    
                    <!-- Promo Code -->
                    <div class="card mt-3">
                        <div class="card-body">
                            <h6>Have a promo code?</h6>
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Enter code">
                                <button class="btn btn-outline-primary" type="button">Apply</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            {% else %}
            <!-- Empty Cart -->
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart fa-5x text-muted mb-4"></i>
                <h3>Your cart is empty</h3>
                <p class="text-muted mb-4">Looks like you haven't added any items to your cart yet.</p>
                <a href="{% url 'store:product_list' %}" class="btn btn-primary btn-lg">
                    <i class="fas fa-shopping-bag me-2"></i>Start Shopping
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function updateQuantity(productId, size, quantity) {
        if (quantity < 1) {
            removeFromCart(productId, size);
            return;
        }
        
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        
        fetch('{% url "store:cart_update" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': csrfToken
            },
            body: `product_id=${productId}&size=${size}&quantity=${quantity}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload(); // Reload to update the cart display
            } else {
                showNotification('Error updating cart', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error updating cart', 'error');
        });
    }
    
    function removeFromCart(productId, size) {
        if (!confirm('Are you sure you want to remove this item from your cart?')) {
            return;
        }
        
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        
        fetch('{% url "store:cart_remove" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': csrfToken
            },
            body: `product_id=${productId}&size=${size}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload(); // Reload to update the cart display
            } else {
                showNotification('Error removing item', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error removing item', 'error');
        });
    }
    
    function clearCart() {
        if (!confirm('Are you sure you want to clear your entire cart?')) {
            return;
        }
        
        // Clear all items one by one (you could also create a clear_cart endpoint)
        const cartItems = document.querySelectorAll('.cart-item');
        cartItems.forEach(item => {
            const productId = item.querySelector('.quantity-input').dataset.productId;
            const size = item.querySelector('.quantity-input').dataset.size;
            removeFromCart(productId, size);
        });
    }
</script>
{% endblock %}
