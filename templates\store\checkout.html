{% extends 'base.html' %}
{% load static %}

{% block title %}Checkout - Fashion Store{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">Checkout</h2>
            
            <!-- Progress Steps -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="progress-steps d-flex justify-content-between">
                        <div class="step active">
                            <div class="step-number">1</div>
                            <div class="step-title">Shipping</div>
                        </div>
                        <div class="step">
                            <div class="step-number">2</div>
                            <div class="step-title">Payment</div>
                        </div>
                        <div class="step">
                            <div class="step-number">3</div>
                            <div class="step-title">Confirmation</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <!-- Checkout Form -->
                <div class="col-lg-8">
                    <form method="post" id="checkout-form">
                        {% csrf_token %}
                        
                        <!-- Shipping Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Shipping Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="first_name" class="form-label">First Name *</label>
                                        <input type="text" class="form-control" id="first_name" name="first_name" 
                                               value="{{ user.first_name }}" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="last_name" class="form-label">Last Name *</label>
                                        <input type="text" class="form-control" id="last_name" name="last_name" 
                                               value="{{ user.last_name }}" required>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email *</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="{{ user.email }}" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Phone *</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="{{ profile.phone }}" required>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="address" class="form-label">Address *</label>
                                    <textarea class="form-control" id="address" name="address" rows="3" required>{{ profile.address }}</textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="city" class="form-label">City *</label>
                                        <input type="text" class="form-control" id="city" name="city" 
                                               value="{{ profile.city }}" required>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="postal_code" class="form-label">Postal Code *</label>
                                        <input type="text" class="form-control" id="postal_code" name="postal_code" 
                                               value="{{ profile.postal_code }}" required>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="country" class="form-label">Country *</label>
                                        <input type="text" class="form-control" id="country" name="country" 
                                               value="{{ profile.country }}" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Payment Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    This is a demo store. No real payment will be processed.
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="payment_method" id="credit_card" value="credit_card" checked>
                                    <label class="form-check-label" for="credit_card">
                                        <i class="fas fa-credit-card me-2"></i>Credit Card
                                    </label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="payment_method" id="paypal" value="paypal">
                                    <label class="form-check-label" for="paypal">
                                        <i class="fab fa-paypal me-2"></i>PayPal
                                    </label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="payment_method" id="cash_on_delivery" value="cash_on_delivery">
                                    <label class="form-check-label" for="cash_on_delivery">
                                        <i class="fas fa-money-bill-wave me-2"></i>Cash on Delivery
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-credit-card me-2"></i>Place Order
                        </button>
                    </form>
                </div>
                
                <!-- Order Summary -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Order Summary</h5>
                        </div>
                        <div class="card-body">
                            {% for item in cart %}
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    {% if item.product.image %}
                                    <img src="{{ item.product.image.url }}" class="rounded" width="60" height="60" style="object-fit: cover;" alt="{{ item.product.name }}">
                                    {% else %}
                                    <div class="bg-light d-flex align-items-center justify-content-center rounded" style="width: 60px; height: 60px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1">{{ item.product.name }}</h6>
                                    <small class="text-muted">
                                        {% if item.size %}Size: {{ item.size }} | {% endif %}Qty: {{ item.quantity }}
                                    </small>
                                    <div class="fw-bold">${{ item.total_price }}</div>
                                </div>
                            </div>
                            {% endfor %}
                            
                            <hr>
                            
                            <div class="d-flex justify-content-between mb-2">
                                <span>Subtotal:</span>
                                <span>${{ cart.get_total_price }}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Shipping:</span>
                                <span>Free</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Tax:</span>
                                <span>$0.00</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between">
                                <strong>Total:</strong>
                                <strong>${{ cart.get_total_price }}</strong>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Security Info -->
                    <div class="card mt-3">
                        <div class="card-body text-center">
                            <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                            <h6>Secure Checkout</h6>
                            <small class="text-muted">Your information is protected by SSL encryption</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .progress-steps {
        margin-bottom: 2rem;
    }
    
    .step {
        text-align: center;
        position: relative;
        flex: 1;
    }
    
    .step:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 20px;
        left: 60%;
        right: -40%;
        height: 2px;
        background-color: #e9ecef;
        z-index: 1;
    }
    
    .step.active:not(:last-child)::after {
        background-color: var(--primary-color);
    }
    
    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 10px;
        font-weight: bold;
        position: relative;
        z-index: 2;
    }
    
    .step.active .step-number {
        background-color: var(--primary-color);
        color: white;
    }
    
    .step-title {
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .step.active .step-title {
        color: var(--primary-color);
        font-weight: 600;
    }
</style>
{% endblock %}
